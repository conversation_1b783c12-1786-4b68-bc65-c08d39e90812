"""
Anthropic Claude LLM provider implementation.
"""

import json
import asyncio
from typing import Dict, Any, Optional, List
import httpx

from ..llm_factory import BaseLLMProvider
from ...utils import get_logger, LLMError, retry_on_exception, NetworkError

logger = get_logger(__name__)


class AnthropicProvider(BaseLLMProvider):
    """Anthropic Claude LLM provider."""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229", **kwargs):
        super().__init__(api_key, model, **kwargs)
        self.base_url = kwargs.get("base_url", "https://api.anthropic.com/v1")
        self.client = httpx.AsyncClient(timeout=60.0)
        
        # Default generation config
        self.generation_config = {
            "max_tokens": kwargs.get("max_tokens", 4096),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 1.0),
        }
        
        # Anthropic API version
        self.api_version = kwargs.get("api_version", "2023-06-01")
    
    def validate_configuration(self) -> bool:
        """Validate Anthropic configuration."""
        if not self.api_key:
            raise LLMError("Anthropic API key is required")
        
        if not self.model:
            raise LLMError("Anthropic model name is required")
        
        return True
    
    @retry_on_exception((NetworkError, httpx.RequestError))
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate a response using Anthropic Claude."""
        try:
            url = f"{self.base_url}/messages"
            
            # Merge generation config with kwargs
            generation_config = {**self.generation_config, **kwargs}
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                **generation_config
            }
            
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "anthropic-version": self.api_version
            }
            
            self.logger.debug(
                "Sending request to Anthropic",
                model=self.model,
                prompt_length=len(prompt)
            )
            
            response = await self.client.post(
                url,
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                error_msg = f"Anthropic API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                raise NetworkError(error_msg)
            
            result = response.json()
            
            # Extract the generated text
            if "content" in result and len(result["content"]) > 0:
                content_block = result["content"][0]
                if "text" in content_block:
                    generated_text = content_block["text"]
                    
                    self.logger.debug(
                        "Received response from Anthropic",
                        response_length=len(generated_text)
                    )
                    
                    return generated_text
            
            raise LLMError(f"Unexpected response format from Anthropic: {result}")
            
        except httpx.RequestError as e:
            self.logger.error(f"Network error calling Anthropic: {e}")
            raise NetworkError(f"Network error calling Anthropic: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON response from Anthropic: {e}")
            raise LLMError(f"Invalid JSON response from Anthropic: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error calling Anthropic: {e}")
            raise LLMError(f"Unexpected error calling Anthropic: {e}")
    
    async def generate_structured_response(self, prompt: str, schema: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Generate a structured response using Anthropic with JSON schema."""
        try:
            # Add JSON schema instruction to prompt
            schema_prompt = f"""
{prompt}

Please respond with a valid JSON object that matches this schema:
{json.dumps(schema, indent=2)}

Respond only with the JSON object, no additional text or formatting.
"""
            
            response_text = await self.generate_response(schema_prompt, **kwargs)
            
            # Try to parse as JSON
            try:
                # Clean up the response (remove markdown formatting if present)
                cleaned_response = response_text.strip()
                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith("```"):
                    cleaned_response = cleaned_response[:-3]
                cleaned_response = cleaned_response.strip()
                
                result = json.loads(cleaned_response)
                
                self.logger.debug(
                    "Successfully parsed structured response",
                    schema_keys=list(schema.keys()) if isinstance(schema, dict) else None
                )
                
                return result
                
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse JSON response: {e}")
                self.logger.debug(f"Raw response: {response_text}")
                raise LLMError(f"Failed to parse structured response as JSON: {e}")
                
        except Exception as e:
            self.logger.error(f"Error generating structured response: {e}")
            raise
    
    async def generate_with_tools(self, prompt: str, tools: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """Generate response with function calling capabilities."""
        try:
            url = f"{self.base_url}/messages"
            
            generation_config = {**self.generation_config, **kwargs}
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "tools": tools,
                **generation_config
            }
            
            headers = {
                "Content-Type": "application/json",
                "x-api-key": self.api_key,
                "anthropic-version": self.api_version
            }
            
            response = await self.client.post(
                url,
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                error_msg = f"Anthropic API error: {response.status_code} - {response.text}"
                raise NetworkError(error_msg)
            
            result = response.json()
            
            self.logger.debug(
                "Received tool response from Anthropic",
                has_content=bool(result.get("content"))
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating response with tools: {e}")
            raise
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            asyncio.create_task(self.close())
        except:
            pass
