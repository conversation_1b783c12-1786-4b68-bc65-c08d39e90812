"""
Browser lifecycle management and configuration.
"""

import asyncio
import time
from typing import Optional, Dict, Any, List
from pathlib import Path
from uuid import uuid4
import json

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

from ..utils import config, get_logger, BrowserConnectionError, retry_on_exception, safe_execute_async
from ..utils.enhanced_config import ENHANCED_CONFIG
from ..events import default_event_bus
from ..events.browser_events import (
    BrowserLaunchEvent, BrowserConnectedEvent, BrowserStoppedEvent,
    NavigationStartedEvent, NavigationCompleteEvent, TabCreatedEvent, TabClosedEvent
)
from .stealth_config import StealthConfig
from .session_manager import SessionManager

logger = get_logger(__name__)


class BrowserManager:
    """Manages browser instances and their lifecycle."""
    
    def __init__(self, browser_config: Dict[str, Any] = None):
        self.config = browser_config or config.get_browser_config()
        self.logger = get_logger(f"browser_manager")
        self.stealth_config = StealthConfig()
        
        # Browser instance tracking
        self.browser = None
        self.context = None
        self.page = None
        self.is_initialized = False
        
        # Session management
        self.session_data = {}
        self.screenshots_dir = Path("logs/screenshots")
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)

        # Enhanced browser-use style session management
        self.session_manager = SessionManager()
        self.event_bus = default_event_bus

        # Browser-use style state tracking
        self.browser_id: Optional[str] = None
        self.cdp_url: Optional[str] = None
        self.tabs: Dict[str, Dict[str, Any]] = {}
        self.current_tab_id: Optional[str] = None

        # Performance tracking
        self._start_time: Optional[float] = None
        self._connection_time: Optional[float] = None
    
    async def initialize(self) -> bool:
        """Initialize the browser instance."""
        try:
            if not PLAYWRIGHT_AVAILABLE:
                raise BrowserConnectionError("Playwright is not installed. Please install it with: pip install playwright")

            self.logger.info("Initializing browser", browser_type=self.config["browser_type"])
            self._start_time = time.time()

            # Emit browser launch event
            launch_event = BrowserLaunchEvent(
                profile_name=self.config.get('profile_name'),
                headless=self.config.get('headless', False)
            )
            await self.event_bus.emit(launch_event)

            # Start playwright
            self.playwright = await async_playwright().start()
            
            # Launch browser
            browser_type = getattr(self.playwright, self.config["browser_type"])
            
            launch_options = {
                "headless": self.config["headless"],
                "args": self._get_browser_args()
            }
            
            # Add stealth options if enabled
            if self.config.get("stealth", True):
                launch_options.update(self.stealth_config.get_launch_options())
            
            self.browser = await browser_type.launch(**launch_options)

            # Generate browser ID and track connection
            self.browser_id = str(uuid4())
            self._connection_time = time.time()

            # Create browser context
            context_options = {
                "viewport": self.config.get("viewport", {"width": 1920, "height": 1080}),
                "user_agent": self.config.get("user_agent")
            }
            
            # Add stealth context options
            if self.config.get("stealth", True):
                context_options.update(self.stealth_config.get_context_options())
            
            self.context = await self.browser.new_context(**context_options)
            
            # Apply stealth modifications
            if self.config.get("stealth", True):
                await self.stealth_config.apply_stealth_modifications(self.context)
            
            # Create initial page and track as tab
            self.page = await self.context.new_page()
            initial_tab_id = str(uuid4())
            self.current_tab_id = initial_tab_id
            self.tabs[initial_tab_id] = {
                "page": self.page,
                "url": "about:blank",
                "title": "New Tab",
                "created_at": time.time()
            }

            # Set default timeout
            self.page.set_default_timeout(self.config.get("timeout", 30000))

            # Emit tab created event
            tab_event = TabCreatedEvent(target_id=initial_tab_id, url="about:blank")
            await self.event_bus.emit(tab_event)

            # Emit browser connected event
            connected_event = BrowserConnectedEvent(
                browser_id=self.browser_id,
                cdp_url=self.cdp_url or "local"
            )
            await self.event_bus.emit(connected_event)

            self.is_initialized = True
            self.logger.info(f"Browser initialized successfully (ID: {self.browser_id})")

            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize browser: {e}")
            await self.cleanup()
            raise BrowserConnectionError(f"Failed to initialize browser: {e}")
    
    def _get_browser_args(self) -> List[str]:
        """Get browser launch arguments."""
        args = [
            "--no-sandbox",
            "--disable-blink-features=AutomationControlled",
            "--disable-dev-shm-usage",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-images",  # Faster loading
            "--disable-javascript-harmony-shipping",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection"
        ]
        
        # Add additional args from config
        if "browser_args" in self.config:
            args.extend(self.config["browser_args"])
        
        return args
    
    @retry_on_exception((BrowserConnectionError,))
    async def get_page(self) -> Any:
        """Get the current page instance."""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.page or self.page.is_closed():
            self.page = await self.context.new_page()
            self.page.set_default_timeout(self.config.get("timeout", 30000))
        
        return self.page
    
    async def new_page(self) -> Any:
        """Create a new page instance."""
        if not self.is_initialized:
            await self.initialize()
        
        page = await self.context.new_page()
        page.set_default_timeout(self.config.get("timeout", 30000))
        
        self.logger.debug("Created new page")
        return page
    
    async def close_page(self, page: Any = None):
        """Close a specific page or the current page."""
        target_page = page or self.page
        if target_page and not target_page.is_closed():
            await target_page.close()
            self.logger.debug("Closed page")
    
    async def navigate(self, url: str, page: Any = None) -> bool:
        """Navigate to a URL."""
        try:
            target_page = page or await self.get_page()
            
            self.logger.info(f"Navigating to: {url}")

            # Emit navigation started event
            nav_start_event = NavigationStartedEvent(
                url=url,
                target_id=self.current_tab_id or "unknown"
            )
            await self.event_bus.emit(nav_start_event)

            response = await target_page.goto(url, wait_until="domcontentloaded")
            
            if response and response.status >= 400:
                self.logger.warning(f"Navigation returned status {response.status}")
                # Emit navigation failed event
                nav_failed_event = NavigationCompleteEvent(
                    url=url,
                    target_id=self.current_tab_id or "unknown",
                    success=False
                )
                await self.event_bus.emit(nav_failed_event)
                return False

            # Wait for page to be ready
            await target_page.wait_for_load_state("networkidle", timeout=10000)

            # Update tab info
            if self.current_tab_id and self.current_tab_id in self.tabs:
                self.tabs[self.current_tab_id].update({
                    "url": url,
                    "title": await target_page.title(),
                    "last_navigation": time.time()
                })

            # Emit navigation complete event
            nav_complete_event = NavigationCompleteEvent(
                url=url,
                target_id=self.current_tab_id or "unknown",
                success=True
            )
            await self.event_bus.emit(nav_complete_event)

            self.logger.info(f"Successfully navigated to: {url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Navigation failed: {e}")

            # Emit navigation failed event
            nav_failed_event = NavigationCompleteEvent(
                url=url,
                target_id=self.current_tab_id or "unknown",
                success=False
            )
            await self.event_bus.emit(nav_failed_event)

            return False
    
    async def take_screenshot(self, page: Any = None, filename: str = None) -> Optional[str]:
        """Take a screenshot of the current page."""
        try:
            target_page = page or await self.get_page()
            
            if not filename:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                filename = f"screenshot_{timestamp}.png"
            
            screenshot_path = self.screenshots_dir / filename
            
            await target_page.screenshot(path=str(screenshot_path), full_page=True)
            
            self.logger.debug(f"Screenshot saved: {screenshot_path}")
            return str(screenshot_path)
            
        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            return None
    
    async def get_page_content(self, page: Any = None) -> str:
        """Get the current page content."""
        try:
            target_page = page or await self.get_page()
            content = await target_page.content()
            return content
        except Exception as e:
            self.logger.error(f"Failed to get page content: {e}")
            return ""
    
    async def get_page_title(self, page: Any = None) -> str:
        """Get the current page title."""
        try:
            target_page = page or await self.get_page()
            title = await target_page.title()
            return title
        except Exception as e:
            self.logger.error(f"Failed to get page title: {e}")
            return ""
    
    async def get_current_url(self, page: Any = None) -> str:
        """Get the current page URL."""
        try:
            target_page = page or await self.get_page()
            url = target_page.url
            return url
        except Exception as e:
            self.logger.error(f"Failed to get current URL: {e}")
            return ""
    
    async def wait_for_element(self, selector: str, page: Any = None, timeout: int = None) -> Any:
        """Wait for an element to appear."""
        try:
            target_page = page or await self.get_page()
            timeout = timeout or self.config.get("timeout", 30000)
            
            element = await target_page.wait_for_selector(selector, timeout=timeout)
            return element
            
        except Exception as e:
            self.logger.error(f"Element not found: {selector} - {e}")
            return None
    
    async def execute_script(self, script: str, page: Any = None) -> Any:
        """Execute JavaScript on the page."""
        try:
            target_page = page or await self.get_page()
            result = await target_page.evaluate(script)
            return result
        except Exception as e:
            self.logger.error(f"Script execution failed: {e}")
            return None
    
    async def save_session(self, session_name: str = "default") -> bool:
        """Save the current browser session."""
        try:
            if not self.context:
                return False
            
            session_dir = Path("logs/sessions")
            session_dir.mkdir(parents=True, exist_ok=True)
            
            session_file = session_dir / f"{session_name}.json"
            
            # Save cookies and local storage
            cookies = await self.context.cookies()
            
            session_data = {
                "cookies": cookies,
                "url": await self.get_current_url(),
                "timestamp": str(asyncio.get_event_loop().time())
            }
            
            with open(session_file, "w") as f:
                json.dump(session_data, f, indent=2)
            
            self.logger.info(f"Session saved: {session_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save session: {e}")
            return False
    
    async def load_session(self, session_name: str = "default") -> bool:
        """Load a saved browser session."""
        try:
            session_dir = Path("logs/sessions")
            session_file = session_dir / f"{session_name}.json"
            
            if not session_file.exists():
                self.logger.warning(f"Session file not found: {session_file}")
                return False
            
            with open(session_file, "r") as f:
                session_data = json.load(f)
            
            # Restore cookies
            if "cookies" in session_data:
                await self.context.add_cookies(session_data["cookies"])
            
            # Navigate to saved URL
            if "url" in session_data:
                await self.navigate(session_data["url"])
            
            self.logger.info(f"Session loaded: {session_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load session: {e}")
            return False
    
    async def cleanup(self):
        """Clean up browser resources with browser-use style event handling."""
        try:
            # Emit browser stopped event
            if self.browser_id:
                stopped_event = BrowserStoppedEvent(
                    browser_id=self.browser_id,
                    reason="cleanup"
                )
                await self.event_bus.emit(stopped_event)

            # Close all tracked tabs
            for tab_id, tab_info in self.tabs.items():
                try:
                    if tab_info["page"] and not tab_info["page"].is_closed():
                        await tab_info["page"].close()

                    # Emit tab closed event
                    tab_closed_event = TabClosedEvent(target_id=tab_id)
                    await self.event_bus.emit(tab_closed_event)
                except Exception as tab_error:
                    self.logger.warning(f"Error closing tab {tab_id}: {tab_error}")

            if self.page and not self.page.is_closed():
                await self.page.close()

            if self.context:
                await self.context.close()

            if self.browser:
                await self.browser.close()

            if hasattr(self, 'playwright'):
                await self.playwright.stop()

            # Clear state
            self.tabs.clear()
            self.current_tab_id = None
            self.browser_id = None
            self.is_initialized = False

            self.logger.info("Browser cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
