"""
Base watchdog class for browser monitoring components.
Based on browser-use patterns for production reliability.
"""

import asyncio
import inspect
import time
from typing import Any, ClassVar, List, Type
from abc import ABC, abstractmethod

from pydantic import BaseModel, ConfigDict, Field

from ..events import EventBus, Event
from ..utils import get_logger

logger = get_logger(__name__)


class BaseWatchdog(BaseModel, ABC):
    """
    Base class for all browser watchdogs.
    
    Watchdogs monitor browser state and emit events based on changes.
    They automatically register event handlers based on method names.
    
    Handler methods should be named: on_EventTypeName(self, event: EventTypeName)
    """
    
    model_config = ConfigDict(
        arbitrary_types_allowed=True,  # allow non-serializable objects like EventBus
        extra='forbid',  # don't allow implicit class/instance state
        validate_assignment=False,  # avoid re-triggering __init__ on assignments
        revalidate_instances='never',  # avoid re-triggering __init__ and erasing private attrs
    )
    
    # Class variables to statically define the list of events relevant to each watchdog
    LISTENS_TO: ClassVar[List[Type[Event]]] = []  # Events this watchdog listens to
    EMITS: ClassVar[List[Type[Event]]] = []  # Events this watchdog emits
    
    # Core dependencies
    event_bus: EventBus = Field(...)
    browser_manager: Any = Field(...)  # BrowserManager reference
    
    # Watchdog state
    is_active: bool = Field(default=False)
    start_time: float = Field(default_factory=time.time)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.logger = get_logger(f"{self.__class__.__name__}")
        self._tasks: List[asyncio.Task] = []
        self._handlers_registered = False
    
    async def start(self):
        """Start the watchdog."""
        if self.is_active:
            self.logger.warning("Watchdog already active")
            return
        
        try:
            self.logger.debug(f"Starting {self.__class__.__name__}")
            
            # Register event handlers
            await self._register_event_handlers()
            
            # Start watchdog-specific monitoring
            await self._start_monitoring()
            
            self.is_active = True
            self.start_time = time.time()
            
            self.logger.debug(f"{self.__class__.__name__} started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start {self.__class__.__name__}: {e}")
            raise
    
    async def stop(self):
        """Stop the watchdog."""
        if not self.is_active:
            return
        
        try:
            self.logger.debug(f"Stopping {self.__class__.__name__}")
            
            # Cancel all tasks
            for task in self._tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            self._tasks.clear()
            
            # Stop watchdog-specific monitoring
            await self._stop_monitoring()
            
            # Unregister event handlers
            await self._unregister_event_handlers()
            
            self.is_active = False
            
            self.logger.debug(f"{self.__class__.__name__} stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping {self.__class__.__name__}: {e}")
    
    async def _register_event_handlers(self):
        """Register event handlers based on method names."""
        if self._handlers_registered:
            return
        
        # Find all methods that start with 'on_' and register them as event handlers
        for method_name in dir(self):
            if method_name.startswith('on_') and callable(getattr(self, method_name)):
                # Extract event type from method name (e.g., on_NavigationEvent -> NavigationEvent)
                event_type_name = method_name[3:]  # Remove 'on_' prefix
                
                # Get the method
                handler = getattr(self, method_name)
                
                # Register with event bus (simplified - in real implementation would need proper event type mapping)
                self.logger.debug(f"Registering handler {method_name} for {event_type_name}")
        
        self._handlers_registered = True
    
    async def _unregister_event_handlers(self):
        """Unregister event handlers."""
        # In a real implementation, this would unregister handlers from the event bus
        self._handlers_registered = False
    
    @abstractmethod
    async def _start_monitoring(self):
        """Start watchdog-specific monitoring. Override in subclasses."""
        pass
    
    @abstractmethod
    async def _stop_monitoring(self):
        """Stop watchdog-specific monitoring. Override in subclasses."""
        pass
    
    def _create_task(self, coro, name: str = None):
        """Create and track an asyncio task."""
        task = asyncio.create_task(coro, name=name)
        self._tasks.append(task)
        return task
    
    async def _emit_event(self, event: Event):
        """Emit an event through the event bus."""
        try:
            await self.event_bus.emit(event)
        except Exception as e:
            self.logger.error(f"Failed to emit event {event.__class__.__name__}: {e}")
    
    def get_status(self) -> dict:
        """Get watchdog status information."""
        return {
            "name": self.__class__.__name__,
            "is_active": self.is_active,
            "start_time": self.start_time,
            "uptime": time.time() - self.start_time if self.is_active else 0,
            "active_tasks": len([t for t in self._tasks if not t.done()]),
            "total_tasks": len(self._tasks),
            "listens_to": [cls.__name__ for cls in self.LISTENS_TO],
            "emits": [cls.__name__ for cls in self.EMITS]
        }


class PerformanceWatchdog(BaseWatchdog):
    """Watchdog for monitoring browser performance metrics."""
    
    # Performance thresholds
    navigation_timeout_threshold: float = Field(default=30.0)
    memory_threshold_mb: float = Field(default=1000.0)
    
    async def _start_monitoring(self):
        """Start performance monitoring."""
        self._create_task(self._monitor_performance(), "performance_monitor")
    
    async def _stop_monitoring(self):
        """Stop performance monitoring."""
        pass  # Tasks are cancelled in base class
    
    async def _monitor_performance(self):
        """Monitor browser performance metrics."""
        while self.is_active:
            try:
                # In a real implementation, this would collect actual performance metrics
                await asyncio.sleep(5.0)  # Check every 5 seconds
                
                # Example: Check memory usage, navigation times, etc.
                # self.logger.debug("Performance check completed")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(1.0)


class CrashWatchdog(BaseWatchdog):
    """Watchdog for detecting browser crashes and connection issues."""
    
    check_interval: float = Field(default=2.0)
    
    async def _start_monitoring(self):
        """Start crash monitoring."""
        self._create_task(self._monitor_browser_health(), "crash_monitor")
    
    async def _stop_monitoring(self):
        """Stop crash monitoring."""
        pass  # Tasks are cancelled in base class
    
    async def _monitor_browser_health(self):
        """Monitor browser health and detect crashes."""
        while self.is_active:
            try:
                # Check if browser is still responsive
                if self.browser_manager.is_initialized:
                    # In a real implementation, this would ping the browser
                    # and detect if it's crashed or unresponsive
                    pass
                
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Crash monitoring error: {e}")
                await asyncio.sleep(1.0)


class WatchdogManager:
    """Manages multiple watchdogs for a browser session."""
    
    def __init__(self, event_bus: EventBus, browser_manager: Any):
        self.event_bus = event_bus
        self.browser_manager = browser_manager
        self.watchdogs: List[BaseWatchdog] = []
        self.logger = get_logger(self.__class__.__name__)
    
    def add_watchdog(self, watchdog_class: Type[BaseWatchdog], **kwargs):
        """Add a watchdog to the manager."""
        watchdog = watchdog_class(
            event_bus=self.event_bus,
            browser_manager=self.browser_manager,
            **kwargs
        )
        self.watchdogs.append(watchdog)
        self.logger.debug(f"Added watchdog: {watchdog_class.__name__}")
    
    async def start_all(self):
        """Start all watchdogs."""
        for watchdog in self.watchdogs:
            try:
                await watchdog.start()
            except Exception as e:
                self.logger.error(f"Failed to start watchdog {watchdog.__class__.__name__}: {e}")
    
    async def stop_all(self):
        """Stop all watchdogs."""
        for watchdog in self.watchdogs:
            try:
                await watchdog.stop()
            except Exception as e:
                self.logger.error(f"Failed to stop watchdog {watchdog.__class__.__name__}: {e}")
    
    def get_status(self) -> List[dict]:
        """Get status of all watchdogs."""
        return [watchdog.get_status() for watchdog in self.watchdogs]
