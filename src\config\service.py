"""
Enhanced configuration system based on browser-use patterns.
Provides comprehensive configuration management with environment variables, profiles, and validation.
"""

import json
import logging
import os
import psutil
from datetime import datetime
from functools import cache
from pathlib import Path
from typing import Any, Dict, Optional, List, Union
from uuid import uuid4

from pydantic import BaseModel, Field, ConfigDict, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


@cache
def is_running_in_docker() -> bool:
    """Detect if we are running in a docker container."""
    try:
        if Path('/.dockerenv').exists() or 'docker' in Path('/proc/1/cgroup').read_text().lower():
            return True
    except Exception:
        pass

    try:
        # if init proc (PID 1) looks like uvicorn/python/uv/etc. then we're in Docker
        init_cmd = ' '.join(psutil.Process(1).cmdline())
        if ('py' in init_cmd) or ('uv' in init_cmd) or ('app' in init_cmd):
            return True
    except Exception:
        pass

    try:
        # if less than 10 total running procs, then we're almost certainly in a container
        if len(psutil.pids()) < 10:
            return True
    except Exception:
        pass

    return False


class OldConfig:
    """Original lazy-loading configuration class for environment variables."""

    # Cache for directory creation tracking
    _dirs_created = False

    @property
    def BROWSER_USE_LOGGING_LEVEL(self) -> str:
        return os.getenv('BROWSER_USE_LOGGING_LEVEL', 'info').lower()

    @property
    def ANONYMIZED_TELEMETRY(self) -> bool:
        return os.getenv('ANONYMIZED_TELEMETRY', 'true').lower()[:1] in 'ty1'

    @property
    def BROWSER_USE_CLOUD_SYNC(self) -> bool:
        return os.getenv('BROWSER_USE_CLOUD_SYNC', str(self.ANONYMIZED_TELEMETRY)).lower()[:1] in 'ty1'

    @property
    def BROWSER_USE_CLOUD_API_URL(self) -> str:
        url = os.getenv('BROWSER_USE_CLOUD_API_URL', 'https://api.browser-use.com')
        assert '://' in url, 'BROWSER_USE_CLOUD_API_URL must be a valid URL'
        return url

    @property
    def BROWSER_USE_CLOUD_UI_URL(self) -> str:
        url = os.getenv('BROWSER_USE_CLOUD_UI_URL', '')
        # Allow empty string as default, only validate if set
        if url and '://' not in url:
            raise AssertionError('BROWSER_USE_CLOUD_UI_URL must be a valid URL if set')
        return url

    # Path configuration
    @property
    def XDG_CACHE_HOME(self) -> Path:
        return Path(os.getenv('XDG_CACHE_HOME', '~/.cache')).expanduser().resolve()

    @property
    def XDG_CONFIG_HOME(self) -> Path:
        return Path(os.getenv('XDG_CONFIG_HOME', '~/.config')).expanduser().resolve()

    @property
    def BROWSER_USE_CONFIG_DIR(self) -> Path:
        path = Path(os.getenv('BROWSER_USE_CONFIG_DIR', str(self.XDG_CONFIG_HOME / 'ai-sourcing-agent'))).expanduser().resolve()
        self._ensure_dirs()
        return path

    @property
    def BROWSER_USE_CONFIG_FILE(self) -> Path:
        return self.BROWSER_USE_CONFIG_DIR / 'config.json'

    @property
    def BROWSER_USE_PROFILES_DIR(self) -> Path:
        path = self.BROWSER_USE_CONFIG_DIR / 'profiles'
        self._ensure_dirs()
        return path

    @property
    def BROWSER_USE_DEFAULT_USER_DATA_DIR(self) -> Path:
        return self.BROWSER_USE_PROFILES_DIR / 'default'

    @property
    def BROWSER_USE_EXTENSIONS_DIR(self) -> Path:
        path = self.BROWSER_USE_CONFIG_DIR / 'extensions'
        self._ensure_dirs()
        return path

    def _ensure_dirs(self) -> None:
        """Create directories if they don't exist (only once)"""
        if not self._dirs_created:
            config_dir = (
                Path(os.getenv('BROWSER_USE_CONFIG_DIR', str(self.XDG_CONFIG_HOME / 'ai-sourcing-agent'))).expanduser().resolve()
            )
            config_dir.mkdir(parents=True, exist_ok=True)
            (config_dir / 'profiles').mkdir(parents=True, exist_ok=True)
            (config_dir / 'extensions').mkdir(parents=True, exist_ok=True)
            self._dirs_created = True

    # LLM API key configuration
    @property
    def OPENAI_API_KEY(self) -> str:
        return os.getenv('OPENAI_API_KEY', '')

    @property
    def ANTHROPIC_API_KEY(self) -> str:
        return os.getenv('ANTHROPIC_API_KEY', '')

    @property
    def GOOGLE_API_KEY(self) -> str:
        return os.getenv('GOOGLE_API_KEY', '')

    @property
    def DEEPSEEK_API_KEY(self) -> str:
        return os.getenv('DEEPSEEK_API_KEY', '')

    @property
    def GROK_API_KEY(self) -> str:
        return os.getenv('GROK_API_KEY', '')

    @property
    def AZURE_OPENAI_ENDPOINT(self) -> str:
        return os.getenv('AZURE_OPENAI_ENDPOINT', '')

    @property
    def AZURE_OPENAI_KEY(self) -> str:
        return os.getenv('AZURE_OPENAI_KEY', '')

    @property
    def SKIP_LLM_API_KEY_VERIFICATION(self) -> bool:
        return os.getenv('SKIP_LLM_API_KEY_VERIFICATION', 'false').lower()[:1] in 'ty1'

    @property
    def DEFAULT_LLM(self) -> str:
        return os.getenv('DEFAULT_LLM', '')

    # Runtime hints
    @property
    def IN_DOCKER(self) -> bool:
        return os.getenv('IN_DOCKER', 'false').lower()[:1] in 'ty1' or is_running_in_docker()

    @property
    def IS_IN_EVALS(self) -> bool:
        return os.getenv('IS_IN_EVALS', 'false').lower()[:1] in 'ty1'

    @property
    def WIN_FONT_DIR(self) -> str:
        return os.getenv('WIN_FONT_DIR', 'C:\\Windows\\Fonts')


class FlatEnvConfig(BaseSettings):
    """All environment variables in a flat namespace."""

    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', case_sensitive=True, extra='allow')

    # Logging and telemetry
    BROWSER_USE_LOGGING_LEVEL: str = Field(default='info')
    CDP_LOGGING_LEVEL: str = Field(default='WARNING')
    BROWSER_USE_DEBUG_LOG_FILE: Optional[str] = Field(default=None)
    BROWSER_USE_INFO_LOG_FILE: Optional[str] = Field(default=None)
    ANONYMIZED_TELEMETRY: bool = Field(default=True)
    BROWSER_USE_CLOUD_SYNC: Optional[bool] = Field(default=None)
    BROWSER_USE_CLOUD_API_URL: str = Field(default='https://api.browser-use.com')
    BROWSER_USE_CLOUD_UI_URL: str = Field(default='')

    # Path configuration
    XDG_CACHE_HOME: str = Field(default='~/.cache')
    XDG_CONFIG_HOME: str = Field(default='~/.config')
    BROWSER_USE_CONFIG_DIR: Optional[str] = Field(default=None)

    # LLM API keys
    OPENAI_API_KEY: str = Field(default='')
    ANTHROPIC_API_KEY: str = Field(default='')
    GOOGLE_API_KEY: str = Field(default='')
    DEEPSEEK_API_KEY: str = Field(default='')
    GROK_API_KEY: str = Field(default='')
    AZURE_OPENAI_ENDPOINT: str = Field(default='')
    AZURE_OPENAI_KEY: str = Field(default='')
    SKIP_LLM_API_KEY_VERIFICATION: bool = Field(default=False)
    DEFAULT_LLM: str = Field(default='')

    # Runtime hints
    IN_DOCKER: Optional[bool] = Field(default=None)
    IS_IN_EVALS: bool = Field(default=False)
    WIN_FONT_DIR: str = Field(default='C:\\Windows\\Fonts')

    # Agent-specific env vars
    BROWSER_USE_CONFIG_PATH: Optional[str] = Field(default=None)
    BROWSER_USE_HEADLESS: Optional[bool] = Field(default=None)
    BROWSER_USE_ALLOWED_DOMAINS: Optional[str] = Field(default=None)
    BROWSER_USE_LLM_MODEL: Optional[str] = Field(default=None)

    # Proxy env vars
    BROWSER_USE_PROXY_URL: Optional[str] = Field(default=None)
    BROWSER_USE_NO_PROXY: Optional[str] = Field(default=None)
    BROWSER_USE_PROXY_USERNAME: Optional[str] = Field(default=None)
    BROWSER_USE_PROXY_PASSWORD: Optional[str] = Field(default=None)


class ProxySettings(BaseModel):
    """Proxy configuration settings."""

    server: Optional[str] = Field(None, description="Proxy URL, e.g. http://host:8080")
    bypass: Optional[str] = Field(None, description="Comma-separated hosts to bypass")
    username: Optional[str] = Field(None, description="Proxy auth username")
    password: Optional[str] = Field(None, description="Proxy auth password")


class DBStyleEntry(BaseModel):
    """Database-style entry with UUID and metadata."""

    id: str = Field(default_factory=lambda: str(uuid4()))
    default: bool = Field(default=False)
    created_at: str = Field(default_factory=lambda: datetime.utcnow().isoformat())


class BrowserProfileEntry(DBStyleEntry):
    """Browser profile configuration entry - accepts any BrowserProfile fields."""

    model_config = ConfigDict(extra='allow')

    # Common browser profile fields for reference
    headless: Optional[bool] = None
    user_data_dir: Optional[str] = None
    allowed_domains: Optional[List[str]] = None
    downloads_path: Optional[str] = None
    browser_type: Optional[str] = None
    executable_path: Optional[str] = None
    window_width: Optional[int] = None
    window_height: Optional[int] = None
    timeout: Optional[float] = None
    wait_between_actions: Optional[float] = None
    disable_security: Optional[bool] = None
    enable_stealth: Optional[bool] = None
    user_agent: Optional[str] = None
    proxy: Optional[ProxySettings] = None
    args: Optional[List[str]] = None
    keep_alive: Optional[bool] = None


class BrowserProfile(BaseModel):
    """Browser profile configuration."""

    id: str = Field(default_factory=lambda: str(uuid4()), description="Profile ID")
    name: str = Field("default", description="Profile name")

    # Browser settings
    headless: bool = Field(False, description="Run browser in headless mode")
    browser_type: str = Field("chromium", description="Browser type")
    executable_path: Optional[str] = Field(None, description="Custom browser executable path")

    # Window settings
    window_width: int = Field(1920, description="Browser window width")
    window_height: int = Field(1080, description="Browser window height")
    window_position: Optional[Dict[str, int]] = Field(None, description="Window position")

    # Performance settings
    timeout: float = Field(30.0, description="Default timeout in seconds")
    wait_between_actions: float = Field(0.5, description="Wait time between actions")
    minimum_wait_page_load_time: float = Field(1.0, description="Minimum page load wait time")

    # Security and stealth
    disable_security: bool = Field(False, description="Disable browser security features")
    enable_stealth: bool = Field(True, description="Enable stealth mode")
    user_agent: Optional[str] = Field(None, description="Custom user agent")

    # Extensions and features
    enable_extensions: bool = Field(True, description="Enable browser extensions")
    enable_default_extensions: bool = Field(True, description="Enable default automation extensions")

    # Proxy settings
    proxy: Optional[ProxySettings] = Field(None, description="Proxy configuration")

    # Domain restrictions
    allowed_domains: Optional[List[str]] = Field(None, description="Allowed domains list")

    # User data
    user_data_dir: Optional[str] = Field(None, description="User data directory path")
    profile_directory: str = Field("Default", description="Profile directory name")

    # Advanced settings
    args: List[str] = Field(default_factory=list, description="Additional browser arguments")
    keep_alive: bool = Field(False, description="Keep browser alive after agent run")


class LLMEntry(DBStyleEntry):
    """LLM configuration entry."""

    api_key: Optional[str] = None
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    provider: Optional[str] = None
    base_url: Optional[str] = None
    timeout: Optional[float] = None
    max_retries: Optional[int] = None


class AgentEntry(DBStyleEntry):
    """Agent configuration entry."""

    max_steps: Optional[int] = None
    use_vision: Optional[bool] = None
    system_prompt: Optional[str] = None
    max_failures: Optional[int] = None
    max_actions_per_step: Optional[int] = None
    use_thinking: Optional[bool] = None
    flash_mode: Optional[bool] = None
    llm_timeout: Optional[int] = None
    step_timeout: Optional[int] = None


class DBStyleConfigJSON(BaseModel):
    """New database-style configuration format."""

    browser_profile: Dict[str, BrowserProfileEntry] = Field(default_factory=dict)
    llm: Dict[str, LLMEntry] = Field(default_factory=dict)
    agent: Dict[str, AgentEntry] = Field(default_factory=dict)


class LLMConfig(BaseModel):
    """LLM provider configuration."""

    id: str = Field(default_factory=lambda: str(uuid4()), description="Config ID")
    name: str = Field("default", description="Config name")

    # Provider settings
    provider: str = Field("gemini", description="LLM provider name")
    model: str = Field("gemini-2.0-flash-exp", description="Model name")
    api_key: Optional[str] = Field(None, description="API key")
    base_url: Optional[str] = Field(None, description="Custom API base URL")

    # Generation parameters
    temperature: float = Field(0.7, description="Generation temperature")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    top_p: Optional[float] = Field(None, description="Top-p sampling parameter")
    top_k: Optional[int] = Field(None, description="Top-k sampling parameter")

    # Timeout and retry settings
    timeout: float = Field(30.0, description="Request timeout in seconds")
    max_retries: int = Field(3, description="Maximum number of retries")
    retry_delay: float = Field(1.0, description="Delay between retries")

    # Cost tracking
    enable_cost_tracking: bool = Field(True, description="Enable cost tracking")

    # Provider-specific settings
    extra_params: Dict[str, Any] = Field(default_factory=dict, description="Provider-specific parameters")


class AgentConfig(BaseModel):
    """Agent configuration settings."""
    
    id: str = Field(default_factory=lambda: str(uuid4()), description="Config ID")
    name: str = Field("default", description="Config name")
    
    # Agent behavior
    max_steps: int = Field(50, description="Maximum number of steps")
    step_timeout: float = Field(60.0, description="Timeout per step in seconds")
    
    # Action settings
    enable_screenshots: bool = Field(True, description="Enable screenshot capture")
    screenshot_quality: int = Field(80, description="Screenshot quality (1-100)")
    
    # Memory and context
    max_memory_items: int = Field(100, description="Maximum memory items to keep")
    context_window_size: int = Field(10, description="Context window size for conversation")
    
    # Error handling
    max_retries: int = Field(3, description="Maximum retries for failed actions")
    continue_on_error: bool = Field(True, description="Continue execution on non-critical errors")
    
    # Observability
    enable_telemetry: bool = Field(True, description="Enable telemetry collection")
    log_level: str = Field("INFO", description="Logging level")
    
    # Custom settings
    custom_instructions: Optional[str] = Field(None, description="Custom agent instructions")
    excluded_actions: List[str] = Field(default_factory=list, description="Actions to exclude")


class EnvironmentConfig(BaseSettings):
    """Environment variable configuration."""
    
    model_config = SettingsConfigDict(
        env_file='.env',
        env_file_encoding='utf-8',
        case_sensitive=True,
        extra='allow'
    )
    
    # Logging and telemetry
    AI_SOURCING_LOGGING_LEVEL: str = Field(default='INFO')
    AI_SOURCING_DEBUG_LOG_FILE: Optional[str] = Field(default=None)
    AI_SOURCING_INFO_LOG_FILE: Optional[str] = Field(default=None)
    ANONYMIZED_TELEMETRY: bool = Field(default=True)
    
    # Path configuration
    XDG_CACHE_HOME: str = Field(default='~/.cache')
    XDG_CONFIG_HOME: str = Field(default='~/.config')
    AI_SOURCING_CONFIG_DIR: Optional[str] = Field(default=None)
    
    # LLM API keys
    OPENAI_API_KEY: str = Field(default='')
    ANTHROPIC_API_KEY: str = Field(default='')
    GOOGLE_API_KEY: str = Field(default='')
    GEMINI_API_KEY: str = Field(default='')  # Alias for GOOGLE_API_KEY
    
    # Browser settings
    AI_SOURCING_HEADLESS: Optional[bool] = Field(default=None)
    AI_SOURCING_BROWSER_TYPE: Optional[str] = Field(default=None)
    
    # Agent settings
    AI_SOURCING_MAX_STEPS: Optional[int] = Field(default=None)
    AI_SOURCING_STEP_TIMEOUT: Optional[float] = Field(default=None)
    
    # Proxy settings
    AI_SOURCING_PROXY_URL: Optional[str] = Field(default=None)
    AI_SOURCING_PROXY_USERNAME: Optional[str] = Field(default=None)
    AI_SOURCING_PROXY_PASSWORD: Optional[str] = Field(default=None)
    
    # Runtime hints
    IN_DOCKER: Optional[bool] = Field(default=None)
    
    @field_validator('GEMINI_API_KEY', mode='before')
    @classmethod
    def set_google_api_key_alias(cls, v):
        """Use GEMINI_API_KEY as alias for GOOGLE_API_KEY."""
        # Note: In Pydantic v2, we can't access other field values in field validators
        # This functionality would need to be handled differently if needed
        return v


class ConfigurationService:
    """
    Enhanced configuration service based on browser-use patterns.
    
    Features:
    - Environment variable support
    - JSON configuration files
    - Profile management
    - Configuration validation
    - Migration support
    """
    
    def __init__(self):
        self.env_config = EnvironmentConfig()
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        self._dirs_created = False
        
        # Configuration cache
        self._browser_profiles: Dict[str, BrowserProfile] = {}
        self._llm_configs: Dict[str, LLMConfig] = {}
        self._agent_configs: Dict[str, AgentConfig] = {}
    
    @property
    def config_dir(self) -> Path:
        """Get configuration directory path."""
        if self.env_config.AI_SOURCING_CONFIG_DIR:
            path = Path(self.env_config.AI_SOURCING_CONFIG_DIR).expanduser().resolve()
        else:
            xdg_config = Path(self.env_config.XDG_CONFIG_HOME).expanduser().resolve()
            path = xdg_config / 'ai-sourcing-agent'
        
        self._ensure_dirs()
        return path
    
    @property
    def config_file(self) -> Path:
        """Get main configuration file path."""
        return self.config_dir / 'config.json'
    
    @property
    def profiles_dir(self) -> Path:
        """Get profiles directory path."""
        path = self.config_dir / 'profiles'
        self._ensure_dirs()
        return path
    
    def _ensure_dirs(self):
        """Create configuration directories if they don't exist."""
        if not self._dirs_created:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            (self.config_dir / 'profiles').mkdir(parents=True, exist_ok=True)
            (self.config_dir / 'logs').mkdir(parents=True, exist_ok=True)
            self._dirs_created = True
    
    def load_config(self) -> Dict[str, Any]:
        """Load complete configuration from file and environment."""
        config_data = {
            'browser_profiles': {},
            'llm_configs': {},
            'agent_configs': {}
        }
        
        # Load from file if exists
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    file_config = json.load(f)
                    config_data.update(file_config)
            except Exception as e:
                self.logger.warning(f"Failed to load config file: {e}")
        
        # Apply environment variable overrides
        config_data = self._apply_env_overrides(config_data)
        
        # Ensure default configurations exist
        config_data = self._ensure_defaults(config_data)
        
        return config_data
    
    def save_config(self, config_data: Dict[str, Any]):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2, default=str)
            self.logger.debug(f"Configuration saved to {self.config_file}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    def get_browser_profile(self, name: str = "default") -> BrowserProfile:
        """Get browser profile by name."""
        if name not in self._browser_profiles:
            config_data = self.load_config()
            profiles = config_data.get('browser_profiles', {})
            
            if name in profiles:
                self._browser_profiles[name] = BrowserProfile(**profiles[name])
            else:
                # Create default profile
                self._browser_profiles[name] = BrowserProfile(name=name)
        
        return self._browser_profiles[name]
    
    def get_llm_config(self, name: str = "default") -> LLMConfig:
        """Get LLM configuration by name."""
        if name not in self._llm_configs:
            config_data = self.load_config()
            configs = config_data.get('llm_configs', {})
            
            if name in configs:
                self._llm_configs[name] = LLMConfig(**configs[name])
            else:
                # Create default config
                self._llm_configs[name] = LLMConfig(name=name)
        
        return self._llm_configs[name]
    
    def get_agent_config(self, name: str = "default") -> AgentConfig:
        """Get agent configuration by name."""
        if name not in self._agent_configs:
            config_data = self.load_config()
            configs = config_data.get('agent_configs', {})
            
            if name in configs:
                self._agent_configs[name] = AgentConfig(**configs[name])
            else:
                # Create default config
                self._agent_configs[name] = AgentConfig(name=name)
        
        return self._agent_configs[name]
    
    def _apply_env_overrides(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration."""
        # Browser profile overrides
        default_browser = config_data.get('browser_profiles', {}).get('default', {})
        
        if self.env_config.AI_SOURCING_HEADLESS is not None:
            default_browser['headless'] = self.env_config.AI_SOURCING_HEADLESS
        
        if self.env_config.AI_SOURCING_BROWSER_TYPE:
            default_browser['browser_type'] = self.env_config.AI_SOURCING_BROWSER_TYPE
        
        # LLM config overrides
        default_llm = config_data.get('llm_configs', {}).get('default', {})
        
        # Use the first available API key
        api_keys = {
            'openai': self.env_config.OPENAI_API_KEY,
            'anthropic': self.env_config.ANTHROPIC_API_KEY,
            'gemini': self.env_config.GOOGLE_API_KEY
        }
        
        for provider, key in api_keys.items():
            if key and not default_llm.get('api_key'):
                default_llm['provider'] = provider
                default_llm['api_key'] = key
                break
        
        # Agent config overrides
        default_agent = config_data.get('agent_configs', {}).get('default', {})
        
        if self.env_config.AI_SOURCING_MAX_STEPS:
            default_agent['max_steps'] = self.env_config.AI_SOURCING_MAX_STEPS
        
        if self.env_config.AI_SOURCING_STEP_TIMEOUT:
            default_agent['step_timeout'] = self.env_config.AI_SOURCING_STEP_TIMEOUT
        
        # Update config data
        if default_browser:
            config_data.setdefault('browser_profiles', {})['default'] = default_browser
        if default_llm:
            config_data.setdefault('llm_configs', {})['default'] = default_llm
        if default_agent:
            config_data.setdefault('agent_configs', {})['default'] = default_agent
        
        return config_data
    
    def _ensure_defaults(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure default configurations exist."""
        # Ensure default browser profile
        if 'default' not in config_data.get('browser_profiles', {}):
            config_data.setdefault('browser_profiles', {})['default'] = BrowserProfile().model_dump()
        
        # Ensure default LLM config
        if 'default' not in config_data.get('llm_configs', {}):
            config_data.setdefault('llm_configs', {})['default'] = LLMConfig().model_dump()
        
        # Ensure default agent config
        if 'default' not in config_data.get('agent_configs', {}):
            config_data.setdefault('agent_configs', {})['default'] = AgentConfig().model_dump()
        
        return config_data
    
    def validate_configuration(self) -> bool:
        """Validate the current configuration."""
        try:
            # Load and validate all configurations
            config_data = self.load_config()
            
            # Validate browser profiles
            for name, profile_data in config_data.get('browser_profiles', {}).items():
                BrowserProfile(**profile_data)
            
            # Validate LLM configs
            for name, llm_data in config_data.get('llm_configs', {}).items():
                llm_config = LLMConfig(**llm_data)
                if not llm_config.api_key:
                    self.logger.warning(f"LLM config '{name}' has no API key")
            
            # Validate agent configs
            for name, agent_data in config_data.get('agent_configs', {}).items():
                AgentConfig(**agent_data)
            
            self.logger.info("Configuration validation successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            return False


class Config:
    """Backward-compatible configuration class that merges all config sources.

    Re-reads environment variables on every access to maintain compatibility.
    """

    def __init__(self):
        # Cache for directory creation tracking only
        self._dirs_created = False

    def __getattr__(self, name: str) -> Any:
        """Dynamically proxy all attributes to fresh instances.

        This ensures env vars are re-read on every access.
        """
        # Special handling for internal attributes
        if name.startswith('_'):
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

        # Create fresh instances on every access
        old_config = OldConfig()

        # Always use old config for all attributes (it handles env vars with proper transformations)
        if hasattr(old_config, name):
            return getattr(old_config, name)

        # For new agent-specific attributes not in old config
        env_config = FlatEnvConfig()
        if hasattr(env_config, name):
            return getattr(env_config, name)

        # Handle special methods
        if name == 'get_default_profile':
            return lambda: self._get_default_profile()
        elif name == 'get_default_llm':
            return lambda: self._get_default_llm()
        elif name == 'get_default_agent':
            return lambda: self._get_default_agent()
        elif name == 'load_config':
            return lambda: self._load_config()
        elif name == '_ensure_dirs':
            return lambda: old_config._ensure_dirs()

        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def _get_config_path(self) -> Path:
        """Get config path from fresh env config."""
        env_config = FlatEnvConfig()
        if env_config.BROWSER_USE_CONFIG_PATH:
            return Path(env_config.BROWSER_USE_CONFIG_PATH).expanduser()
        elif env_config.BROWSER_USE_CONFIG_DIR:
            return Path(env_config.BROWSER_USE_CONFIG_DIR).expanduser() / 'config.json'
        else:
            xdg_config = Path(env_config.XDG_CONFIG_HOME).expanduser()
            return xdg_config / 'ai-sourcing-agent' / 'config.json'

    def _get_db_config(self) -> DBStyleConfigJSON:
        """Load and migrate config.json."""
        config_path = self._get_config_path()
        return load_and_migrate_config(config_path)

    def _get_default_profile(self) -> Dict[str, Any]:
        """Get the default browser profile configuration."""
        db_config = self._get_db_config()
        for profile in db_config.browser_profile.values():
            if profile.default:
                return profile.model_dump(exclude_none=True)

        # Return first profile if no default
        if db_config.browser_profile:
            return next(iter(db_config.browser_profile.values())).model_dump(exclude_none=True)

        return {}

    def _get_default_llm(self) -> Dict[str, Any]:
        """Get the default LLM configuration."""
        db_config = self._get_db_config()
        for llm in db_config.llm.values():
            if llm.default:
                return llm.model_dump(exclude_none=True)

        # Return first LLM if no default
        if db_config.llm:
            return next(iter(db_config.llm.values())).model_dump(exclude_none=True)

        return {}

    def _get_default_agent(self) -> Dict[str, Any]:
        """Get the default agent configuration."""
        db_config = self._get_db_config()
        for agent in db_config.agent.values():
            if agent.default:
                return agent.model_dump(exclude_none=True)

        # Return first agent if no default
        if db_config.agent:
            return next(iter(db_config.agent.values())).model_dump(exclude_none=True)

        return {}

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration with env var overrides for components."""
        config = {
            'browser_profile': self._get_default_profile(),
            'llm': self._get_default_llm(),
            'agent': self._get_default_agent(),
        }

        # Fresh env config for overrides
        env_config = FlatEnvConfig()

        # Apply agent-specific env var overrides
        if env_config.BROWSER_USE_HEADLESS is not None:
            config['browser_profile']['headless'] = env_config.BROWSER_USE_HEADLESS

        if env_config.BROWSER_USE_ALLOWED_DOMAINS:
            domains = [d.strip() for d in env_config.BROWSER_USE_ALLOWED_DOMAINS.split(',') if d.strip()]
            config['browser_profile']['allowed_domains'] = domains

        # Proxy settings (Chromium) -> consolidated `proxy` dict
        proxy_dict: Dict[str, Any] = {}
        if env_config.BROWSER_USE_PROXY_URL:
            proxy_dict['server'] = env_config.BROWSER_USE_PROXY_URL
        if env_config.BROWSER_USE_NO_PROXY:
            # store bypass as comma-separated string to match Chrome flag
            proxy_dict['bypass'] = ','.join([d.strip() for d in env_config.BROWSER_USE_NO_PROXY.split(',') if d.strip()])
        if env_config.BROWSER_USE_PROXY_USERNAME:
            proxy_dict['username'] = env_config.BROWSER_USE_PROXY_USERNAME
        if env_config.BROWSER_USE_PROXY_PASSWORD:
            proxy_dict['password'] = env_config.BROWSER_USE_PROXY_PASSWORD
        if proxy_dict:
            # ensure section exists
            config.setdefault('browser_profile', {})
            config['browser_profile']['proxy'] = proxy_dict

        if env_config.GOOGLE_API_KEY:
            config['llm']['api_key'] = env_config.GOOGLE_API_KEY

        if env_config.BROWSER_USE_LLM_MODEL:
            config['llm']['model'] = env_config.BROWSER_USE_LLM_MODEL

        return config


# Create singleton instance
CONFIG = Config()

# Global configuration service instance
config_service = ConfigurationService()

# Helper functions for components
def load_config() -> Dict[str, Any]:
    """Load configuration for components."""
    return CONFIG.load_config()


def get_default_profile(config: Dict[str, Any]) -> Dict[str, Any]:
    """Get default browser profile from config dict."""
    return config.get('browser_profile', {})


def get_default_llm(config: Dict[str, Any]) -> Dict[str, Any]:
    """Get default LLM config from config dict."""
    return config.get('llm', {})
