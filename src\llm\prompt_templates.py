"""
Prompt templates for browser automation tasks.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime


class PromptTemplates:
    """Collection of optimized prompt templates for browser automation."""
    
    @staticmethod
    def browser_task_prompt(
        task: str,
        current_url: str = None,
        page_content: str = None,
        screenshot_description: str = None,
        available_tools: List[str] = None,
        context: Dict[str, Any] = None
    ) -> str:
        """
        Generate a prompt for browser automation tasks.
        
        Args:
            task: The task to perform
            current_url: Current page URL
            page_content: Current page content/DOM
            screenshot_description: Description of current screenshot
            available_tools: List of available browser tools
            context: Additional context information
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            "You are an expert browser automation agent. Your task is to help users automate web browser interactions.",
            "",
            f"**Current Task:** {task}",
            ""
        ]
        
        if current_url:
            prompt_parts.extend([
                f"**Current URL:** {current_url}",
                ""
            ])
        
        if screenshot_description:
            prompt_parts.extend([
                "**Current Page Visual Description:**",
                screenshot_description,
                ""
            ])
        
        if page_content:
            # Truncate page content if too long
            content = page_content[:2000] + "..." if len(page_content) > 2000 else page_content
            prompt_parts.extend([
                "**Current Page Content:**",
                "```html",
                content,
                "```",
                ""
            ])
        
        if available_tools:
            prompt_parts.extend([
                "**Available Tools:**",
                *[f"- {tool}" for tool in available_tools],
                ""
            ])
        
        if context:
            prompt_parts.extend([
                "**Additional Context:**",
                *[f"- {key}: {value}" for key, value in context.items()],
                ""
            ])
        
        prompt_parts.extend([
            "**Instructions:**",
            "1. Analyze the current page state and the requested task",
            "2. Plan the sequence of actions needed to complete the task",
            "3. Execute the actions step by step using the available tools",
            "4. Provide clear feedback on what you're doing and why",
            "5. If you encounter errors, try alternative approaches",
            "6. Confirm task completion when finished",
            "",
            "Please proceed with the task. Be methodical and explain your reasoning."
        ])
        
        return "\n".join(prompt_parts)
    
    @staticmethod
    def element_interaction_prompt(
        action: str,
        element_description: str,
        page_context: str = None,
        value: str = None
    ) -> str:
        """
        Generate a prompt for specific element interactions.
        
        Args:
            action: The action to perform (click, type, etc.)
            element_description: Description of the target element
            page_context: Context about the current page
            value: Value to input (for typing actions)
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            f"I need to {action} on the following element:",
            f"**Element:** {element_description}",
            ""
        ]
        
        if page_context:
            prompt_parts.extend([
                f"**Page Context:** {page_context}",
                ""
            ])
        
        if value:
            prompt_parts.extend([
                f"**Value to Input:** {value}",
                ""
            ])
        
        prompt_parts.extend([
            "Please locate this element and perform the requested action.",
            "If the element is not immediately visible, try scrolling or waiting for it to load.",
            "Provide feedback on the action performed and any results."
        ])
        
        return "\n".join(prompt_parts)
    
    @staticmethod
    def navigation_prompt(
        target_url: str = None,
        navigation_instruction: str = None,
        current_url: str = None
    ) -> str:
        """
        Generate a prompt for navigation tasks.
        
        Args:
            target_url: URL to navigate to
            navigation_instruction: Natural language navigation instruction
            current_url: Current page URL
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            "I need help with browser navigation.",
            ""
        ]
        
        if current_url:
            prompt_parts.extend([
                f"**Current URL:** {current_url}",
                ""
            ])
        
        if target_url:
            prompt_parts.extend([
                f"**Target URL:** {target_url}",
                "Please navigate to this URL.",
                ""
            ])
        elif navigation_instruction:
            prompt_parts.extend([
                f"**Navigation Task:** {navigation_instruction}",
                ""
            ])
        
        prompt_parts.extend([
            "Please perform the navigation and confirm when complete.",
            "If there are any redirects or loading issues, please handle them appropriately."
        ])
        
        return "\n".join(prompt_parts)
    
    @staticmethod
    def data_extraction_prompt(
        extraction_target: str,
        page_content: str = None,
        output_format: str = "json",
        specific_fields: List[str] = None
    ) -> str:
        """
        Generate a prompt for data extraction tasks.
        
        Args:
            extraction_target: What data to extract
            page_content: Current page content
            output_format: Desired output format
            specific_fields: Specific fields to extract
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            f"I need to extract the following data: {extraction_target}",
            ""
        ]
        
        if specific_fields:
            prompt_parts.extend([
                "**Specific Fields to Extract:**",
                *[f"- {field}" for field in specific_fields],
                ""
            ])
        
        if page_content:
            content = page_content[:3000] + "..." if len(page_content) > 3000 else page_content
            prompt_parts.extend([
                "**Page Content:**",
                "```html",
                content,
                "```",
                ""
            ])
        
        prompt_parts.extend([
            f"**Output Format:** {output_format.upper()}",
            "",
            "Please extract the requested data and format it according to the specified format.",
            "Ensure the extracted data is accurate and complete.",
            "If some data is not available, indicate this clearly in the output."
        ])
        
        return "\n".join(prompt_parts)
    
    @staticmethod
    def form_filling_prompt(
        form_data: Dict[str, Any],
        form_description: str = None,
        page_content: str = None
    ) -> str:
        """
        Generate a prompt for form filling tasks.
        
        Args:
            form_data: Data to fill in the form
            form_description: Description of the form
            page_content: Current page content
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            "I need to fill out a form with the following data:",
            ""
        ]
        
        if form_description:
            prompt_parts.extend([
                f"**Form Description:** {form_description}",
                ""
            ])
        
        prompt_parts.extend([
            "**Form Data:**",
            *[f"- {field}: {value}" for field, value in form_data.items()],
            ""
        ])
        
        if page_content:
            content = page_content[:2000] + "..." if len(page_content) > 2000 else page_content
            prompt_parts.extend([
                "**Page Content:**",
                "```html",
                content,
                "```",
                ""
            ])
        
        prompt_parts.extend([
            "**Instructions:**",
            "1. Locate the form fields on the page",
            "2. Fill in each field with the corresponding data",
            "3. Handle any special field types (dropdowns, checkboxes, etc.)",
            "4. Validate that the data was entered correctly",
            "5. Submit the form if requested",
            "",
            "Please proceed with filling out the form step by step."
        ])
        
        return "\n".join(prompt_parts)
    
    @staticmethod
    def error_recovery_prompt(
        error_description: str,
        attempted_action: str,
        page_state: str = None,
        suggested_alternatives: List[str] = None
    ) -> str:
        """
        Generate a prompt for error recovery scenarios.
        
        Args:
            error_description: Description of the error
            attempted_action: What action was being attempted
            page_state: Current state of the page
            suggested_alternatives: Alternative approaches to try
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            "An error occurred during browser automation. I need help recovering.",
            "",
            f"**Error:** {error_description}",
            f"**Attempted Action:** {attempted_action}",
            ""
        ]
        
        if page_state:
            prompt_parts.extend([
                f"**Current Page State:** {page_state}",
                ""
            ])
        
        if suggested_alternatives:
            prompt_parts.extend([
                "**Suggested Alternatives:**",
                *[f"- {alt}" for alt in suggested_alternatives],
                ""
            ])
        
        prompt_parts.extend([
            "**Recovery Instructions:**",
            "1. Analyze the error and current page state",
            "2. Determine the best recovery approach",
            "3. Try alternative methods to achieve the original goal",
            "4. If recovery is not possible, provide clear feedback",
            "",
            "Please help me recover from this error and continue with the task."
        ])
        
        return "\n".join(prompt_parts)
    
    @staticmethod
    def task_planning_prompt(
        high_level_task: str,
        available_tools: List[str] = None,
        constraints: List[str] = None,
        context: Dict[str, Any] = None
    ) -> str:
        """
        Generate a prompt for task planning and decomposition.
        
        Args:
            high_level_task: The high-level task to plan
            available_tools: Available browser automation tools
            constraints: Any constraints or limitations
            context: Additional context information
        
        Returns:
            str: Formatted prompt
        """
        prompt_parts = [
            "I need help planning a complex browser automation task.",
            "",
            f"**High-Level Task:** {high_level_task}",
            ""
        ]
        
        if available_tools:
            prompt_parts.extend([
                "**Available Tools:**",
                *[f"- {tool}" for tool in available_tools],
                ""
            ])
        
        if constraints:
            prompt_parts.extend([
                "**Constraints:**",
                *[f"- {constraint}" for constraint in constraints],
                ""
            ])
        
        if context:
            prompt_parts.extend([
                "**Context:**",
                *[f"- {key}: {value}" for key, value in context.items()],
                ""
            ])
        
        prompt_parts.extend([
            "**Planning Request:**",
            "Please break down this high-level task into a sequence of specific, actionable steps.",
            "Each step should be clear and executable using the available tools.",
            "Consider potential error scenarios and include recovery steps.",
            "Provide the plan in a structured format that can be executed step by step."
        ])
        
        return "\n".join(prompt_parts)
