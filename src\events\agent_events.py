"""
Agent-specific events for the automation system.
"""

from typing import Any, Dict, List, Optional
from pydantic import Field

from .event_bus import Event


class AgentEvent(Event):
    """Base class for agent-related events."""
    
    agent_id: str = Field(...)
    agent_name: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Agent event for {self.agent_id}"


class TaskStartEvent(AgentEvent):
    """Event fired when a task starts."""
    
    task_id: str = Field(...)
    task_description: str = Field(...)
    max_steps: int = Field(default=10)
    expected_duration: Optional[float] = Field(default=None)
    task_priority: int = Field(default=0)
    task_metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def get_description(self) -> str:
        return f"Task started: {self.task_description[:100]}..."


class TaskCompleteEvent(AgentEvent):
    """Event fired when a task completes successfully."""
    
    task_id: str = Field(...)
    task_description: str = Field(...)
    execution_time: float = Field(...)
    steps_taken: int = Field(...)
    result: Optional[Dict[str, Any]] = Field(default=None)
    success_metrics: Optional[Dict[str, float]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Task completed in {self.execution_time:.2f}s with {self.steps_taken} steps"


class TaskFailEvent(AgentEvent):
    """Event fired when a task fails."""
    
    task_id: str = Field(...)
    task_description: str = Field(...)
    execution_time: float = Field(...)
    steps_taken: int = Field(...)
    error_message: str = Field(...)
    error_type: str = Field(...)
    recoverable: bool = Field(default=False)
    retry_count: int = Field(default=0)
    failure_context: Optional[Dict[str, Any]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Task failed after {self.steps_taken} steps: {self.error_message}"


class StepStartEvent(AgentEvent):
    """Event fired when a step starts."""
    
    task_id: str = Field(...)
    step_number: int = Field(...)
    max_steps: int = Field(...)
    step_description: Optional[str] = Field(default=None)
    step_type: Optional[str] = Field(default=None)
    expected_actions: Optional[List[str]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Step {self.step_number}/{self.max_steps} started"


class StepCompleteEvent(AgentEvent):
    """Event fired when a step completes."""
    
    task_id: str = Field(...)
    step_number: int = Field(...)
    execution_time: float = Field(...)
    actions_taken: List[str] = Field(default_factory=list)
    step_result: Optional[Dict[str, Any]] = Field(default=None)
    is_final_step: bool = Field(default=False)
    next_step_planned: bool = Field(default=True)
    
    def get_description(self) -> str:
        return f"Step {self.step_number} completed in {self.execution_time:.2f}s"


class ActionPlanEvent(AgentEvent):
    """Event fired when an action plan is generated."""
    
    task_id: str = Field(...)
    step_number: int = Field(...)
    planned_actions: List[Dict[str, Any]] = Field(...)
    reasoning: Optional[str] = Field(default=None)
    confidence_score: Optional[float] = Field(default=None)
    alternative_plans: Optional[List[Dict[str, Any]]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Action plan generated with {len(self.planned_actions)} actions"


class ActionExecuteEvent(AgentEvent):
    """Event fired when an action is executed."""
    
    task_id: str = Field(...)
    step_number: int = Field(...)
    action_type: str = Field(...)
    action_parameters: Dict[str, Any] = Field(default_factory=dict)
    execution_time: Optional[float] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    result: Optional[Any] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Action executed: {self.action_type}"


class ThinkingEvent(AgentEvent):
    """Event fired when agent is thinking/reasoning."""
    
    task_id: str = Field(...)
    step_number: Optional[int] = Field(default=None)
    thinking_content: str = Field(...)
    thinking_type: str = Field(default="general")  # general, planning, evaluation, recovery
    confidence_level: Optional[float] = Field(default=None)
    
    def get_description(self) -> str:
        content_preview = self.thinking_content[:100] + "..." if len(self.thinking_content) > 100 else self.thinking_content
        return f"Agent thinking ({self.thinking_type}): {content_preview}"


class EvaluationEvent(AgentEvent):
    """Event fired when agent evaluates previous actions."""
    
    task_id: str = Field(...)
    step_number: int = Field(...)
    evaluation_result: str = Field(...)
    success_indicators: List[str] = Field(default_factory=list)
    failure_indicators: List[str] = Field(default_factory=list)
    improvement_suggestions: Optional[List[str]] = Field(default=None)
    overall_score: Optional[float] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Step evaluation: {self.evaluation_result}"


class RecoveryEvent(AgentEvent):
    """Event fired when agent attempts error recovery."""
    
    task_id: str = Field(...)
    step_number: int = Field(...)
    original_error: str = Field(...)
    recovery_strategy: str = Field(...)
    recovery_actions: List[Dict[str, Any]] = Field(default_factory=list)
    recovery_success: Optional[bool] = Field(default=None)
    recovery_result: Optional[Dict[str, Any]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Recovery attempt using strategy: {self.recovery_strategy}"


class StateChangeEvent(AgentEvent):
    """Event fired when agent state changes."""
    
    previous_state: str = Field(...)
    new_state: str = Field(...)
    state_data: Optional[Dict[str, Any]] = Field(default=None)
    trigger: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"State changed from {self.previous_state} to {self.new_state}"


class MemoryEvent(AgentEvent):
    """Event fired for memory operations."""
    
    operation: str = Field(...)  # store, retrieve, update, delete
    memory_type: str = Field(default="short_term")  # short_term, long_term, working
    memory_key: Optional[str] = Field(default=None)
    memory_content: Optional[Any] = Field(default=None)
    memory_size: Optional[int] = Field(default=None)
    success: bool = Field(default=True)
    
    def get_description(self) -> str:
        return f"Memory {self.operation} operation on {self.memory_type}"


class LLMInteractionEvent(AgentEvent):
    """Event fired for LLM interactions."""
    
    task_id: Optional[str] = Field(default=None)
    step_number: Optional[int] = Field(default=None)
    llm_provider: str = Field(...)
    llm_model: str = Field(...)
    prompt_tokens: Optional[int] = Field(default=None)
    completion_tokens: Optional[int] = Field(default=None)
    total_tokens: Optional[int] = Field(default=None)
    response_time: float = Field(...)
    cost: Optional[float] = Field(default=None)
    success: bool = Field(default=True)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"LLM interaction with {self.llm_provider} ({self.llm_model}) - {self.response_time:.2f}s"


class SessionEvent(AgentEvent):
    """Event fired for session management."""
    
    session_id: str = Field(...)
    operation: str = Field(...)  # create, save, load, delete
    session_data: Optional[Dict[str, Any]] = Field(default=None)
    success: bool = Field(default=True)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Session {self.operation}: {self.session_id}"


class PerformanceMetricEvent(AgentEvent):
    """Event fired for performance metrics."""
    
    metric_name: str = Field(...)
    metric_value: float = Field(...)
    metric_unit: str = Field(default="")
    metric_category: str = Field(default="general")  # general, task, step, action
    benchmark_value: Optional[float] = Field(default=None)
    performance_trend: Optional[str] = Field(default=None)  # improving, declining, stable
    
    def get_description(self) -> str:
        return f"Performance metric: {self.metric_name} = {self.metric_value}{self.metric_unit}"


class ConfigurationEvent(AgentEvent):
    """Event fired for configuration changes."""
    
    config_section: str = Field(...)
    config_key: str = Field(...)
    old_value: Optional[Any] = Field(default=None)
    new_value: Any = Field(...)
    change_reason: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Configuration changed: {self.config_section}.{self.config_key}"


class DebugEvent(AgentEvent):
    """Event fired for debugging information."""
    
    debug_level: str = Field(default="info")  # debug, info, warning, error
    debug_message: str = Field(...)
    debug_data: Optional[Dict[str, Any]] = Field(default=None)
    stack_trace: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Debug ({self.debug_level}): {self.debug_message}"
