"""
Error handling utilities and custom exceptions for the browser automation system.
"""

import asyncio
import time
from typing import Optional, Callable, Any, Type, Union, List
from functools import wraps
import traceback

from .config import config
from .logging import get_logger

logger = get_logger(__name__)


class BrowserAutomationError(Exception):
    """Base exception for browser automation errors."""
    pass


class BrowserConnectionError(BrowserAutomationError):
    """Raised when browser connection fails."""
    pass


class ElementNotFoundError(BrowserAutomationError):
    """Raised when a required element is not found."""
    pass


class TaskExecutionError(BrowserAutomationError):
    """Raised when task execution fails."""
    pass


class LLMError(BrowserAutomationError):
    """Raised when LLM operations fail."""
    pass


class ConfigurationError(BrowserAutomationError):
    """Raised when configuration is invalid."""
    pass


class TimeoutError(BrowserAutomationError):
    """Raised when operations timeout."""
    pass


class RetryableError(BrowserAutomationError):
    """Base class for errors that can be retried."""
    pass


class NetworkError(RetryableError):
    """Raised when network operations fail."""
    pass


class TemporaryBrowserError(RetryableError):
    """Raised when browser encounters temporary issues."""
    pass


def retry_on_exception(
    exceptions: Union[Type[Exception], tuple] = (RetryableError,),
    max_attempts: int = None,
    delay: float = None,
    backoff_factor: float = 2.0,
    max_delay: float = 60.0
):
    """
    Decorator to retry function execution on specific exceptions.
    
    Args:
        exceptions: Exception types to retry on
        max_attempts: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff_factor: Factor to multiply delay by after each attempt
        max_delay: Maximum delay between retries
    """
    if max_attempts is None:
        max_attempts = config.performance.retry_attempts
    if delay is None:
        delay = config.performance.retry_delay
    
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        break
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed, retrying in {current_delay}s",
                        function=func.__name__,
                        error=str(e),
                        attempt=attempt + 1,
                        max_attempts=max_attempts
                    )
                    
                    await asyncio.sleep(current_delay)
                    current_delay = min(current_delay * backoff_factor, max_delay)
            
            logger.error(
                f"All {max_attempts} attempts failed",
                function=func.__name__,
                error=str(last_exception)
            )
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        break
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed, retrying in {current_delay}s",
                        function=func.__name__,
                        error=str(e),
                        attempt=attempt + 1,
                        max_attempts=max_attempts
                    )
                    
                    time.sleep(current_delay)
                    current_delay = min(current_delay * backoff_factor, max_delay)
            
            logger.error(
                f"All {max_attempts} attempts failed",
                function=func.__name__,
                error=str(last_exception)
            )
            raise last_exception
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def timeout_after(seconds: float):
    """
    Decorator to add timeout to async functions.
    
    Args:
        seconds: Timeout in seconds
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
            except asyncio.TimeoutError:
                raise TimeoutError(f"Function {func.__name__} timed out after {seconds} seconds")
        return wrapper
    return decorator


class ErrorHandler:
    """Centralized error handling and recovery."""
    
    def __init__(self, agent_id: str = None):
        self.agent_id = agent_id
        self.logger = get_logger(f"error_handler.{agent_id}" if agent_id else "error_handler")
    
    def handle_browser_error(self, error: Exception, context: str = None) -> bool:
        """
        Handle browser-related errors and attempt recovery.
        
        Returns:
            bool: True if error was handled and recovery attempted, False otherwise
        """
        self.logger.error(
            "Browser error occurred",
            error=str(error),
            error_type=type(error).__name__,
            context=context,
            agent_id=self.agent_id
        )
        
        # Classify error and determine if it's recoverable
        if isinstance(error, (BrowserConnectionError, TemporaryBrowserError)):
            self.logger.info("Attempting browser recovery", agent_id=self.agent_id)
            return True
        
        return False
    
    def handle_llm_error(self, error: Exception, context: str = None) -> bool:
        """
        Handle LLM-related errors.
        
        Returns:
            bool: True if error was handled, False otherwise
        """
        self.logger.error(
            "LLM error occurred",
            error=str(error),
            error_type=type(error).__name__,
            context=context,
            agent_id=self.agent_id
        )
        
        # Check if it's a rate limit or temporary error
        error_str = str(error).lower()
        if any(keyword in error_str for keyword in ["rate limit", "quota", "timeout", "temporary"]):
            self.logger.info("LLM error appears to be temporary", agent_id=self.agent_id)
            return True
        
        return False
    
    def handle_task_error(self, error: Exception, task: str = None) -> dict:
        """
        Handle task execution errors and provide recovery suggestions.
        
        Returns:
            dict: Error information and recovery suggestions
        """
        error_info = {
            "error": str(error),
            "error_type": type(error).__name__,
            "task": task,
            "recoverable": False,
            "suggestions": []
        }
        
        self.logger.error(
            "Task execution error",
            **error_info,
            agent_id=self.agent_id
        )
        
        # Provide recovery suggestions based on error type
        if isinstance(error, ElementNotFoundError):
            error_info["recoverable"] = True
            error_info["suggestions"] = [
                "Wait for page to load completely",
                "Try alternative element selectors",
                "Check if page structure has changed"
            ]
        elif isinstance(error, NetworkError):
            error_info["recoverable"] = True
            error_info["suggestions"] = [
                "Check internet connection",
                "Retry the operation",
                "Use alternative network route"
            ]
        elif isinstance(error, TimeoutError):
            error_info["recoverable"] = True
            error_info["suggestions"] = [
                "Increase timeout duration",
                "Check if page is loading slowly",
                "Verify element exists before interaction"
            ]
        
        return error_info


def safe_execute(func: Callable, *args, default_return=None, **kwargs) -> Any:
    """
    Safely execute a function and return default value on error.
    
    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Value to return on error
        **kwargs: Function keyword arguments
    
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.warning(
            "Safe execution failed",
            function=func.__name__,
            error=str(e),
            args=str(args)[:100],  # Limit args length in logs
            kwargs=str(kwargs)[:100]
        )
        return default_return


async def safe_execute_async(func: Callable, *args, default_return=None, **kwargs) -> Any:
    """
    Safely execute an async function and return default value on error.
    
    Args:
        func: Async function to execute
        *args: Function arguments
        default_return: Value to return on error
        **kwargs: Function keyword arguments
    
    Returns:
        Function result or default_return on error
    """
    try:
        return await func(*args, **kwargs)
    except Exception as e:
        logger.warning(
            "Safe async execution failed",
            function=func.__name__,
            error=str(e),
            args=str(args)[:100],
            kwargs=str(kwargs)[:100]
        )
        return default_return


def get_error_context(error: Exception) -> dict:
    """
    Extract detailed context information from an exception.
    
    Args:
        error: Exception to analyze
    
    Returns:
        dict: Error context information
    """
    return {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "traceback": traceback.format_exc(),
        "is_retryable": isinstance(error, RetryableError)
    }
