# Core browser automation library
browser-use>=0.7.7

# Browser automation framework
playwright>=1.40.0

# LLM integrations
openai>=1.0.0
google-generativeai>=0.8.0
anthropic>=0.7.0

# Async and utilities
asyncio-mqtt>=0.16.0
aiofiles>=23.0.0
python-dotenv>=1.0.0

# Data validation and serialization
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Logging and monitoring
structlog>=23.0.0
rich>=13.0.0

# HTTP client
httpx>=0.25.0
requests>=2.31.0

# Image processing (for screenshots)
Pillow>=10.0.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0

# Optional: For advanced features
selenium>=4.15.0  # Fallback browser automation
beautifulsoup4>=4.12.0  # HTML parsing
lxml>=4.9.0  # XML/HTML processing
