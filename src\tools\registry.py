"""
Enhanced action registry based on browser-use patterns.
Provides comprehensive action registration, validation, and execution.
"""

import asyncio
import functools
import inspect
import logging
import time
from collections.abc import Callable
from inspect import Parameter, iscoroutinefunction, signature
from typing import Any, Dict, List, Optional, TypeVar, Union, get_args, get_origin
from uuid import uuid4

from pydantic import BaseModel, Field, ValidationError, create_model

from ..utils import get_logger
from ..browser.browser_manager import BrowserManager
from ..dom.service import DOMService
from ..llm.base import BaseLLMProvider

logger = get_logger(__name__)

Context = TypeVar('Context')


class ActionResult(BaseModel):
    """Result of an action execution."""
    
    success: bool = Field(..., description="Whether the action succeeded")
    action_type: str = Field(..., description="Type of action executed")
    extracted_content: Optional[str] = Field(None, description="Content extracted from the action")
    error: Optional[str] = Field(None, description="Error message if action failed")
    execution_time: float = Field(..., description="Time taken to execute the action")
    
    # Browser-use style fields
    is_done: bool = Field(False, description="Whether the task is complete")
    long_term_memory: Optional[str] = Field(None, description="Information to remember")
    attachments: List[str] = Field(default_factory=list, description="File attachments")
    
    # Metadata
    action_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique action ID")
    timestamp: float = Field(default_factory=time.time, description="Execution timestamp")


class RegisteredAction(BaseModel):
    """Model for a registered action based on browser-use patterns."""
    
    name: str = Field(..., description="Action name")
    description: str = Field(..., description="Action description")
    function: Callable = Field(..., description="Action function")
    param_model: type[BaseModel] = Field(..., description="Parameter model")
    
    # Domain filtering
    domains: Optional[List[str]] = Field(None, description="Allowed domains")
    
    # Metadata
    category: str = Field("general", description="Action category")
    requires_browser: bool = Field(False, description="Whether action requires browser")
    requires_dom: bool = Field(False, description="Whether action requires DOM service")
    requires_llm: bool = Field(False, description="Whether action requires LLM")
    
    # Execution properties
    timeout: float = Field(30.0, description="Action timeout in seconds")
    retries: int = Field(0, description="Number of retries on failure")
    
    class Config:
        arbitrary_types_allowed = True
    
    def prompt_description(self) -> str:
        """Get a description of the action for the prompt."""
        skip_keys = ['title']
        s = f'{self.description}: \n'
        s += '{' + str(self.name) + ': '
        
        if hasattr(self.param_model, 'model_json_schema'):
            schema = self.param_model.model_json_schema()
            properties = schema.get('properties', {})
            s += str({
                k: {sub_k: sub_v for sub_k, sub_v in v.items() if sub_k not in skip_keys}
                for k, v in properties.items()
            })
        
        s += '}'
        return s


class ActionRegistry(BaseModel):
    """Registry for managing actions."""
    
    actions: Dict[str, RegisteredAction] = Field(default_factory=dict)
    
    def get_action(self, name: str) -> Optional[RegisteredAction]:
        """Get action by name."""
        return self.actions.get(name)
    
    def list_actions(self, category: Optional[str] = None) -> List[RegisteredAction]:
        """List all actions, optionally filtered by category."""
        actions = list(self.actions.values())
        if category:
            actions = [a for a in actions if a.category == category]
        return actions
    
    def get_prompt_descriptions(self, current_url: Optional[str] = None) -> List[str]:
        """Get prompt descriptions for all available actions."""
        descriptions = []
        
        for action in self.actions.values():
            # Check domain restrictions
            if current_url and action.domains:
                if not self._url_matches_domains(current_url, action.domains):
                    continue
            
            descriptions.append(action.prompt_description())
        
        return descriptions
    
    def _url_matches_domains(self, url: str, domains: List[str]) -> bool:
        """Check if URL matches any of the allowed domains."""
        # Simplified domain matching - in production, this would be more sophisticated
        for domain in domains:
            if domain in url or domain.replace('*', '') in url:
                return True
        return False


class Registry:
    """
    Enhanced action registry service based on browser-use patterns.
    
    Features:
    - Decorator-based action registration
    - Parameter validation with Pydantic
    - Domain-based filtering
    - Automatic dependency injection
    - Error handling and retries
    """
    
    def __init__(self, exclude_actions: Optional[List[str]] = None):
        self.registry = ActionRegistry()
        self.exclude_actions = exclude_actions or []
        self.logger = get_logger(f"{self.__class__.__name__}")
        
        # Execution statistics
        self.stats = {
            "actions_executed": 0,
            "actions_succeeded": 0,
            "actions_failed": 0,
            "total_execution_time": 0.0
        }
    
    def action(
        self,
        description: str,
        param_model: Optional[type[BaseModel]] = None,
        domains: Optional[List[str]] = None,
        category: str = "general",
        requires_browser: bool = False,
        requires_dom: bool = False,
        requires_llm: bool = False,
        timeout: float = 30.0,
        retries: int = 0
    ):
        """
        Decorator for registering actions based on browser-use patterns.
        
        Args:
            description: Description of what the action does
            param_model: Pydantic model for parameters
            domains: List of allowed domains
            category: Action category
            requires_browser: Whether action needs browser access
            requires_dom: Whether action needs DOM service
            requires_llm: Whether action needs LLM access
            timeout: Action timeout in seconds
            retries: Number of retries on failure
        """
        def decorator(func: Callable):
            # Skip registration if action is excluded
            if func.__name__ in self.exclude_actions:
                return func
            
            # Normalize function signature and create parameter model
            normalized_func, actual_param_model = self._normalize_function_signature(
                func, description, param_model
            )
            
            # Create registered action
            action = RegisteredAction(
                name=func.__name__,
                description=description,
                function=normalized_func,
                param_model=actual_param_model,
                domains=domains,
                category=category,
                requires_browser=requires_browser,
                requires_dom=requires_dom,
                requires_llm=requires_llm,
                timeout=timeout,
                retries=retries
            )
            
            # Register the action
            self.registry.actions[func.__name__] = action
            self.logger.debug(f"Registered action: {func.__name__}")
            
            return normalized_func
        
        return decorator
    
    def _normalize_function_signature(
        self,
        func: Callable,
        description: str,
        param_model: Optional[type[BaseModel]]
    ) -> tuple[Callable, type[BaseModel]]:
        """Normalize function signature for consistent parameter handling."""
        sig = signature(func)
        parameters = list(sig.parameters.values())
        
        # Check for **kwargs (not allowed)
        for param in parameters:
            if param.kind == Parameter.VAR_KEYWORD:
                raise ValueError(
                    f"Action '{func.__name__}' has **{param.name} which is not allowed. "
                    f"Actions must have explicit parameters only."
                )
        
        # Special parameter names that are injected by the system
        special_param_names = {
            'browser_manager', 'dom_service', 'llm_provider',
            'browser_session', 'page_extraction_llm', 'file_system'
        }
        
        # Separate action parameters from special parameters
        action_params = []
        special_params = []
        
        for param in parameters:
            if param.name in special_param_names:
                special_params.append(param)
            else:
                action_params.append(param)
        
        # Create parameter model if not provided
        if param_model is None and action_params:
            # Create dynamic parameter model
            fields = {}
            for param in action_params:
                field_type = param.annotation if param.annotation != Parameter.empty else str
                default = param.default if param.default != Parameter.empty else ...
                fields[param.name] = (field_type, default)
            
            param_model = create_model(f"{func.__name__}Params", **fields)
        elif param_model is None:
            # No parameters, create empty model
            param_model = create_model(f"{func.__name__}Params")
        
        # Create normalized function
        @functools.wraps(func)
        async def normalized_func(params: BaseModel, **special_context):
            # Convert params to dict for function call
            param_dict = params.model_dump() if hasattr(params, 'model_dump') else {}
            
            # Merge with special context
            all_params = {**param_dict, **special_context}
            
            # Call original function
            if iscoroutinefunction(func):
                return await func(**all_params)
            else:
                return func(**all_params)
        
        return normalized_func, param_model
    
    async def execute_action(
        self,
        action_name: str,
        params: Dict[str, Any],
        browser_manager: Optional[BrowserManager] = None,
        dom_service: Optional[DOMService] = None,
        llm_provider: Optional[BaseLLMProvider] = None,
        **kwargs
    ) -> ActionResult:
        """
        Execute a registered action with dependency injection.
        
        Args:
            action_name: Name of the action to execute
            params: Parameters for the action
            browser_manager: Browser manager instance
            dom_service: DOM service instance
            llm_provider: LLM provider instance
            **kwargs: Additional context
            
        Returns:
            ActionResult with execution details
        """
        start_time = time.time()
        
        try:
            # Get registered action
            action = self.registry.get_action(action_name)
            if not action:
                raise ValueError(f"Action '{action_name}' not found")
            
            # Validate parameters
            try:
                validated_params = action.param_model(**params)
            except ValidationError as e:
                raise ValueError(f"Parameter validation failed: {e}")
            
            # Prepare special context
            special_context = {}
            
            if action.requires_browser and browser_manager:
                special_context['browser_manager'] = browser_manager
                special_context['browser_session'] = browser_manager  # Compatibility
            
            if action.requires_dom and dom_service:
                special_context['dom_service'] = dom_service
            
            if action.requires_llm and llm_provider:
                special_context['llm_provider'] = llm_provider
                special_context['page_extraction_llm'] = llm_provider  # Compatibility
            
            # Add any additional context
            special_context.update(kwargs)
            
            # Execute action with timeout
            try:
                result = await asyncio.wait_for(
                    action.function(params=validated_params, **special_context),
                    timeout=action.timeout
                )
                
                execution_time = time.time() - start_time
                
                # Update statistics
                self.stats["actions_executed"] += 1
                self.stats["actions_succeeded"] += 1
                self.stats["total_execution_time"] += execution_time
                
                # Ensure result is ActionResult
                if isinstance(result, ActionResult):
                    result.execution_time = execution_time
                    return result
                else:
                    return ActionResult(
                        success=True,
                        action_type=action_name,
                        extracted_content=str(result) if result else None,
                        execution_time=execution_time
                    )
                
            except asyncio.TimeoutError:
                raise RuntimeError(f"Action '{action_name}' timed out after {action.timeout}s")
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # Update statistics
            self.stats["actions_executed"] += 1
            self.stats["actions_failed"] += 1
            self.stats["total_execution_time"] += execution_time
            
            self.logger.error(f"Action '{action_name}' failed: {e}")
            
            return ActionResult(
                success=False,
                action_type=action_name,
                error=str(e),
                execution_time=execution_time
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        stats = self.stats.copy()
        if stats["actions_executed"] > 0:
            stats["average_execution_time"] = stats["total_execution_time"] / stats["actions_executed"]
            stats["success_rate"] = stats["actions_succeeded"] / stats["actions_executed"]
        else:
            stats["average_execution_time"] = 0.0
            stats["success_rate"] = 0.0
        
        return stats
