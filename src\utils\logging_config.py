"""
Enhanced logging configuration based on browser-use patterns.
Provides comprehensive logging setup with file handlers, custom levels, and third-party logger management.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv

load_dotenv()

from ..config.service import CONFIG


def add_logging_level(level_name: str, level_num: int, method_name: Optional[str] = None):
    """
    Comprehensively adds a new logging level to the `logging` module and the
    currently configured logging class.

    `level_name` becomes an attribute of the `logging` module with the value
    `level_num`. `method_name` becomes a convenience method for both `logging`
    itself and the class returned by `logging.getLoggerClass()` (usually just
    `logging.Logger`). If `method_name` is not specified, `level_name.lower()` is
    used.

    To avoid accidental clobberings of existing attributes, this method will
    raise an `AttributeError` if the level name is already an attribute of the
    `logging` module or if the method name is already present

    Example
    -------
    >>> add_logging_level('TRACE', logging.DEBUG - 5)
    >>> logging.getLogger(__name__).setLevel('TRACE')
    >>> logging.getLogger(__name__).trace('that worked')
    >>> logging.trace('so did this')
    >>> logging.TRACE
    5

    """
    if not method_name:
        method_name = level_name.lower()

    if hasattr(logging, level_name):
        raise AttributeError(f'{level_name} already defined in logging module')
    if hasattr(logging, method_name):
        raise AttributeError(f'{method_name} already defined in logging module')
    if hasattr(logging.getLoggerClass(), method_name):
        raise AttributeError(f'{method_name} already defined in logger class')

    # This method was inspired by the answers to Stack Overflow post
    # http://stackoverflow.com/q/2183233/2988730, especially
    # http://stackoverflow.com/a/13638084/2988730
    def log_for_level(self, message, *args, **kwargs):
        if self.isEnabledFor(level_num):
            self._log(level_num, message, args, **kwargs)

    def log_to_root(message, *args, **kwargs):
        logging.log(level_num, message, *args, **kwargs)

    logging.addLevelName(level_num, level_name)
    setattr(logging, level_name, level_num)
    setattr(logging.getLoggerClass(), method_name, log_for_level)
    setattr(logging, method_name, log_to_root)


def setup_logging(
    stream=None, 
    log_level=None, 
    force_setup=False, 
    debug_log_file=None, 
    info_log_file=None
):
    """Setup logging configuration for AI Sourcing Agent.

    Args:
        stream: Output stream for logs (default: sys.stdout). Can be sys.stderr for MCP mode.
        log_level: Override log level (default: uses CONFIG.BROWSER_USE_LOGGING_LEVEL)
        force_setup: Force reconfiguration even if handlers already exist
        debug_log_file: Path to log file for debug level logs only
        info_log_file: Path to log file for info level logs only
    """
    # Try to add RESULT level, but ignore if it already exists
    try:
        add_logging_level('RESULT', 35)  # This allows ERROR, FATAL and CRITICAL
    except AttributeError:
        pass  # Level already exists, which is fine

    log_type = log_level or CONFIG.BROWSER_USE_LOGGING_LEVEL

    # Check if handlers are already set up
    if logging.getLogger().hasHandlers() and not force_setup:
        return logging.getLogger('ai_sourcing_agent')

    # Clear existing handlers
    root = logging.getLogger()
    root.handlers = []

    class AgentFormatter(logging.Formatter):
        def __init__(self, fmt, log_level):
            super().__init__(fmt)
            self.log_level = log_level

        def format(self, record):
            # Only clean up names in INFO mode, keep everything in DEBUG mode
            if self.log_level > logging.DEBUG and isinstance(record.name, str) and record.name.startswith('src.'):
                # Extract clean component names from logger names
                if 'agents' in record.name:
                    record.name = 'Agent'
                elif 'browser' in record.name:
                    record.name = 'Browser'
                elif 'tools' in record.name:
                    record.name = 'Tools'
                elif 'dom' in record.name:
                    record.name = 'DOM'
                elif 'llm' in record.name:
                    record.name = 'LLM'
                elif record.name.startswith('src.'):
                    # For other modules, use the last part
                    parts = record.name.split('.')
                    if len(parts) >= 2:
                        record.name = parts[-1]
            return super().format(record)

    # Setup single handler for all loggers
    console = logging.StreamHandler(stream or sys.stdout)

    # Determine the log level to use first
    if log_type == 'result':
        log_level = 35  # RESULT level value
    elif log_type == 'debug':
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO

    # Additional setLevel here to filter logs
    if log_type == 'result':
        console.setLevel('RESULT')
        console.setFormatter(AgentFormatter('%(message)s', log_level))
    else:
        console.setLevel(log_level)  # Keep console at original log level (e.g., INFO)
        console.setFormatter(AgentFormatter('%(levelname)-8s [%(name)s] %(message)s', log_level))

    # Configure root logger only
    root.addHandler(console)

    # Add file handlers if specified
    file_handlers = []

    # Create debug log file handler
    if debug_log_file:
        debug_handler = logging.FileHandler(debug_log_file, encoding='utf-8')
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(AgentFormatter('%(asctime)s - %(levelname)-8s [%(name)s] %(message)s', logging.DEBUG))
        file_handlers.append(debug_handler)
        root.addHandler(debug_handler)

    # Create info log file handler
    if info_log_file:
        info_handler = logging.FileHandler(info_log_file, encoding='utf-8')
        info_handler.setLevel(logging.INFO)
        info_handler.setFormatter(AgentFormatter('%(asctime)s - %(levelname)-8s [%(name)s] %(message)s', logging.INFO))
        file_handlers.append(info_handler)
        root.addHandler(info_handler)

    # Configure root logger - use DEBUG if debug file logging is enabled
    effective_log_level = logging.DEBUG if debug_log_file else log_level
    root.setLevel(effective_log_level)

    # Configure ai_sourcing_agent logger
    agent_logger = logging.getLogger('ai_sourcing_agent')
    agent_logger.propagate = False  # Don't propagate to root logger
    agent_logger.addHandler(console)
    for handler in file_handlers:
        agent_logger.addHandler(handler)
    agent_logger.setLevel(effective_log_level)

    # Configure src logger (our main module)
    src_logger = logging.getLogger('src')
    src_logger.propagate = False  # Don't propagate to root logger
    src_logger.addHandler(console)
    for handler in file_handlers:
        src_logger.addHandler(handler)
    src_logger.setLevel(effective_log_level)

    logger = logging.getLogger('ai_sourcing_agent')

    # Silence third-party loggers
    third_party_loggers = [
        'WDM',
        'httpx',
        'selenium',
        'playwright',
        'urllib3',
        'asyncio',
        'openai',
        'httpcore',
        'charset_normalizer',
        'anthropic._base_client',
        'PIL.PngImagePlugin',
        'google_genai',
        'portalocker',
        'websockets',
    ]
    for logger_name in third_party_loggers:
        third_party = logging.getLogger(logger_name)
        third_party.setLevel(logging.ERROR)
        third_party.propagate = False

    return logger


class FIFOHandler(logging.Handler):
    """Non-blocking handler that writes to a named pipe."""

    def __init__(self, fifo_path: str):
        super().__init__()
        self.fifo_path = fifo_path
        Path(fifo_path).parent.mkdir(parents=True, exist_ok=True)

        # Create FIFO if it doesn't exist
        if not os.path.exists(fifo_path):
            os.mkfifo(fifo_path)

        # Don't open the FIFO yet - will open on first write
        self.fd = None

    def emit(self, record):
        try:
            # Open FIFO on first write if not already open
            if self.fd is None:
                try:
                    self.fd = os.open(self.fifo_path, os.O_WRONLY | os.O_NONBLOCK)
                except OSError:
                    # No reader connected yet, skip this message
                    return

            msg = f'{self.format(record)}\n'.encode()
            os.write(self.fd, msg)
        except (OSError, BrokenPipeError):
            # Reader disconnected, close and reset
            if self.fd is not None:
                try:
                    os.close(self.fd)
                except Exception:
                    pass
                self.fd = None

    def close(self):
        if hasattr(self, 'fd') and self.fd is not None:
            try:
                os.close(self.fd)
            except Exception:
                pass
        super().close()


def setup_log_pipes(session_id: str, base_dir: Optional[str] = None):
    """Setup named pipes for log streaming.

    Usage:
        # In agent:
        setup_log_pipes(session_id="abc123")

        # In consumer process:
        tail -f {temp_dir}/agent.c123/agent.pipe
    """
    import tempfile

    if base_dir is None:
        base_dir = tempfile.gettempdir()

    suffix = session_id[-4:]
    pipe_dir = Path(base_dir) / f'agent.{suffix}'

    # Agent logs
    agent_handler = FIFOHandler(str(pipe_dir / 'agent.pipe'))
    agent_handler.setLevel(logging.DEBUG)
    agent_handler.setFormatter(logging.Formatter('%(levelname)-8s [%(name)s] %(message)s'))
    for name in ['src.agents', 'src.tools']:
        logger = logging.getLogger(name)
        logger.addHandler(agent_handler)
        logger.setLevel(logging.DEBUG)
        logger.propagate = True

    # Browser logs
    browser_handler = FIFOHandler(str(pipe_dir / 'browser.pipe'))
    browser_handler.setLevel(logging.DEBUG)
    browser_handler.setFormatter(logging.Formatter('%(levelname)-8s [%(name)s] %(message)s'))
    for name in ['src.browser']:
        logger = logging.getLogger(name)
        logger.addHandler(browser_handler)
        logger.setLevel(logging.DEBUG)
        logger.propagate = True

    # Event logs
    event_handler = FIFOHandler(str(pipe_dir / 'events.pipe'))
    event_handler.setLevel(logging.INFO)
    event_handler.setFormatter(logging.Formatter('%(levelname)-8s [%(name)s] %(message)s'))
    for name in ['src.events']:
        logger = logging.getLogger(name)
        logger.addHandler(event_handler)
        logger.setLevel(logging.INFO)
        logger.propagate = True
