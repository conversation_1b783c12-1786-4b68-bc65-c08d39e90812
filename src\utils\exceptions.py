"""
Enhanced exception classes based on browser-use patterns.
Provides comprehensive error handling with proper exception hierarchy and context.
"""

from typing import Any, Dict, Optional, List
from pydantic import BaseModel


class AgentException(Exception):
    """Base exception for all agent-related errors."""
    
    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        self.message = message
        self.context = context or {}
        super().__init__(message)


class LLMException(Exception):
    """Exception for LLM-related errors."""
    
    def __init__(self, status_code: int, message: str, context: Optional[Dict[str, Any]] = None):
        self.status_code = status_code
        self.message = message
        self.context = context or {}
        super().__init__(f'Error {status_code}: {message}')


class BrowserError(Exception):
    """Browser-related error with structured messaging for LLM context."""
    
    def __init__(
        self, 
        message: str, 
        long_term_memory: Optional[str] = None,
        short_term_memory: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.long_term_memory = long_term_memory  # Message for LLM to remember
        self.short_term_memory = short_term_memory  # Immediate context for LLM
        self.context = context or {}
        super().__init__(message)


class NavigationError(BrowserError):
    """Error during page navigation."""
    
    def __init__(
        self, 
        url: str, 
        message: str, 
        status_code: Optional[int] = None,
        **kwargs
    ):
        self.url = url
        self.status_code = status_code
        context = kwargs.get('context', {})
        context.update({'url': url, 'status_code': status_code})
        super().__init__(message, context=context, **kwargs)


class ElementNotFoundError(BrowserError):
    """Error when an element cannot be found."""
    
    def __init__(
        self, 
        selector: str, 
        message: Optional[str] = None,
        **kwargs
    ):
        self.selector = selector
        message = message or f"Element not found: {selector}"
        context = kwargs.get('context', {})
        context.update({'selector': selector})
        super().__init__(message, context=context, **kwargs)


class ActionExecutionError(BrowserError):
    """Error during action execution."""
    
    def __init__(
        self, 
        action: str, 
        message: str,
        element_info: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        self.action = action
        self.element_info = element_info
        context = kwargs.get('context', {})
        context.update({'action': action, 'element_info': element_info})
        super().__init__(message, context=context, **kwargs)


class TimeoutError(BrowserError):
    """Error when an operation times out."""

    def __init__(
        self,
        operation: str,
        timeout: float,
        message: Optional[str] = None,
        **kwargs
    ):
        self.operation = operation
        self.timeout = timeout
        message = message or f"Operation '{operation}' timed out after {timeout}s"
        context = kwargs.get('context', {})
        context.update({'operation': operation, 'timeout': timeout})
        super().__init__(message, context=context, **kwargs)


class LLMError(AgentException):
    """Error related to LLM operations."""

    def __init__(
        self,
        message: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        error_code: Optional[str] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        context.update({
            'provider': provider,
            'model': model,
            'error_code': error_code
        })
        super().__init__(message, context=context)


class DOMError(AgentException):
    """Error related to DOM operations."""

    def __init__(
        self,
        message: str,
        element_info: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        context.update({
            'element_info': element_info
        })
        super().__init__(message, context=context)


class AccessibilityError(DOMError):
    """Error related to accessibility operations."""

    def __init__(
        self,
        message: str,
        accessibility_info: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        context = kwargs.get('context', {})
        context.update({
            'accessibility_info': accessibility_info
        })
        super().__init__(message, context=context)


class ConfigurationError(AgentException):
    """Error in configuration setup or validation."""
    
    def __init__(
        self, 
        config_key: str, 
        message: str,
        expected_type: Optional[str] = None,
        actual_value: Optional[Any] = None
    ):
        self.config_key = config_key
        self.expected_type = expected_type
        self.actual_value = actual_value
        context = {
            'config_key': config_key,
            'expected_type': expected_type,
            'actual_value': actual_value
        }
        super().__init__(message, context=context)


class ValidationError(AgentException):
    """Error during data validation."""
    
    def __init__(
        self, 
        field: str, 
        message: str,
        value: Optional[Any] = None,
        constraints: Optional[Dict[str, Any]] = None
    ):
        self.field = field
        self.value = value
        self.constraints = constraints or {}
        context = {
            'field': field,
            'value': value,
            'constraints': constraints
        }
        super().__init__(message, context=context)


class ToolExecutionError(AgentException):
    """Error during tool execution."""
    
    def __init__(
        self, 
        tool_name: str, 
        message: str,
        parameters: Optional[Dict[str, Any]] = None,
        error_details: Optional[str] = None
    ):
        self.tool_name = tool_name
        self.parameters = parameters or {}
        self.error_details = error_details
        context = {
            'tool_name': tool_name,
            'parameters': parameters,
            'error_details': error_details
        }
        super().__init__(message, context=context)


class DOMProcessingError(AgentException):
    """Error during DOM processing."""
    
    def __init__(
        self, 
        message: str,
        dom_info: Optional[Dict[str, Any]] = None,
        processing_stage: Optional[str] = None
    ):
        self.dom_info = dom_info or {}
        self.processing_stage = processing_stage
        context = {
            'dom_info': dom_info,
            'processing_stage': processing_stage
        }
        super().__init__(message, context=context)


class SessionError(BrowserError):
    """Error related to browser session management."""
    
    def __init__(
        self, 
        session_id: str, 
        message: str,
        session_state: Optional[str] = None,
        **kwargs
    ):
        self.session_id = session_id
        self.session_state = session_state
        context = kwargs.get('context', {})
        context.update({'session_id': session_id, 'session_state': session_state})
        super().__init__(message, context=context, **kwargs)


class RecoveryError(AgentException):
    """Error during error recovery attempts."""
    
    def __init__(
        self, 
        original_error: Exception, 
        recovery_attempts: List[str],
        message: Optional[str] = None
    ):
        self.original_error = original_error
        self.recovery_attempts = recovery_attempts
        message = message or f"Failed to recover from {type(original_error).__name__}: {original_error}"
        context = {
            'original_error': str(original_error),
            'original_error_type': type(original_error).__name__,
            'recovery_attempts': recovery_attempts
        }
        super().__init__(message, context=context)


class MaxRetriesExceededError(AgentException):
    """Error when maximum retry attempts are exceeded."""
    
    def __init__(
        self, 
        operation: str, 
        max_retries: int,
        last_error: Optional[Exception] = None
    ):
        self.operation = operation
        self.max_retries = max_retries
        self.last_error = last_error
        message = f"Maximum retries ({max_retries}) exceeded for operation: {operation}"
        context = {
            'operation': operation,
            'max_retries': max_retries,
            'last_error': str(last_error) if last_error else None,
            'last_error_type': type(last_error).__name__ if last_error else None
        }
        super().__init__(message, context=context)


def handle_browser_error(e: BrowserError) -> Dict[str, Any]:
    """Handle browser error and return structured result for LLM."""
    result = {
        'error': e.long_term_memory or e.message,
        'success': False,
        'is_done': False
    }
    
    if e.short_term_memory:
        result['extracted_content'] = e.short_term_memory
        result['include_extracted_content_only_once'] = True
    
    return result


def create_recovery_context(
    error: Exception, 
    recovery_attempts: List[str],
    additional_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Create context information for error recovery."""
    context = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'recovery_attempts': recovery_attempts,
        'timestamp': __import__('datetime').datetime.now().isoformat()
    }
    
    if additional_context:
        context.update(additional_context)
    
    # Add error-specific context if available
    if hasattr(error, 'context'):
        context['error_context'] = error.context
    
    return context
