"""
OpenAI LLM provider implementation.
"""

import json
import asyncio
from typing import Dict, Any, Optional, List
import httpx

from ..llm_factory import BaseLLMProvider
from ...utils import get_logger, LLMError, retry_on_exception, NetworkError

logger = get_logger(__name__)


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider."""
    
    def __init__(self, api_key: str, model: str = "gpt-4", **kwargs):
        super().__init__(api_key, model, **kwargs)
        self.base_url = kwargs.get("base_url", "https://api.openai.com/v1")
        self.client = httpx.AsyncClient(timeout=60.0)
        
        # Default generation config
        self.generation_config = {
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 4096),
            "top_p": kwargs.get("top_p", 1.0),
            "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
            "presence_penalty": kwargs.get("presence_penalty", 0.0),
        }
    
    def validate_configuration(self) -> bool:
        """Validate OpenAI configuration."""
        if not self.api_key:
            raise LLMError("OpenAI API key is required")
        
        if not self.model:
            raise LLMError("OpenAI model name is required")
        
        return True
    
    @retry_on_exception((NetworkError, httpx.RequestError))
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate a response using OpenAI."""
        try:
            url = f"{self.base_url}/chat/completions"
            
            # Merge generation config with kwargs
            generation_config = {**self.generation_config, **kwargs}
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                **generation_config
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            self.logger.debug(
                "Sending request to OpenAI",
                model=self.model,
                prompt_length=len(prompt)
            )
            
            response = await self.client.post(
                url,
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                error_msg = f"OpenAI API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                raise NetworkError(error_msg)
            
            result = response.json()
            
            # Extract the generated text
            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    generated_text = choice["message"]["content"]
                    
                    self.logger.debug(
                        "Received response from OpenAI",
                        response_length=len(generated_text)
                    )
                    
                    return generated_text
            
            raise LLMError(f"Unexpected response format from OpenAI: {result}")
            
        except httpx.RequestError as e:
            self.logger.error(f"Network error calling OpenAI: {e}")
            raise NetworkError(f"Network error calling OpenAI: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON response from OpenAI: {e}")
            raise LLMError(f"Invalid JSON response from OpenAI: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error calling OpenAI: {e}")
            raise LLMError(f"Unexpected error calling OpenAI: {e}")
    
    async def generate_structured_response(self, prompt: str, schema: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Generate a structured response using OpenAI with JSON mode."""
        try:
            # Add JSON schema instruction to prompt
            schema_prompt = f"""
{prompt}

Please respond with a valid JSON object that matches this schema:
{json.dumps(schema, indent=2)}

Respond only with the JSON object, no additional text or formatting.
"""
            
            # Use JSON mode if available
            generation_config = {**kwargs}
            if "response_format" not in generation_config:
                generation_config["response_format"] = {"type": "json_object"}
            
            response_text = await self.generate_response(schema_prompt, **generation_config)
            
            # Try to parse as JSON
            try:
                result = json.loads(response_text.strip())
                
                self.logger.debug(
                    "Successfully parsed structured response",
                    schema_keys=list(schema.keys()) if isinstance(schema, dict) else None
                )
                
                return result
                
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse JSON response: {e}")
                self.logger.debug(f"Raw response: {response_text}")
                raise LLMError(f"Failed to parse structured response as JSON: {e}")
                
        except Exception as e:
            self.logger.error(f"Error generating structured response: {e}")
            raise
    
    async def generate_with_tools(self, prompt: str, tools: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """Generate response with function calling capabilities."""
        try:
            url = f"{self.base_url}/chat/completions"
            
            generation_config = {**self.generation_config, **kwargs}
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "tools": [{"type": "function", "function": tool} for tool in tools],
                **generation_config
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            response = await self.client.post(
                url,
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                error_msg = f"OpenAI API error: {response.status_code} - {response.text}"
                raise NetworkError(error_msg)
            
            result = response.json()
            
            self.logger.debug(
                "Received tool response from OpenAI",
                has_choices=bool(result.get("choices"))
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating response with tools: {e}")
            raise
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            asyncio.create_task(self.close())
        except:
            pass
