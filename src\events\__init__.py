"""
Event system for browser automation.
"""

from .event_bus import EventBus, Event

# Create default event bus instance
default_event_bus = EventBus()
from .browser_events import (
    BrowserEvent,
    NavigationEvent,
    ClickEvent,
    TypeEvent,
    ScrollEvent,
    PageLoadEvent,
    ErrorEvent
)
from .agent_events import (
    AgentEvent,
    TaskStartEvent,
    TaskCompleteEvent,
    TaskFailEvent,
    StepStartEvent,
    StepCompleteEvent
)

__all__ = [
    "EventBus",
    "Event",
    "default_event_bus",
    "BrowserEvent",
    "NavigationEvent", 
    "ClickEvent",
    "TypeEvent",
    "ScrollEvent",
    "PageLoadEvent",
    "ErrorEvent",
    "AgentEvent",
    "TaskStartEvent",
    "TaskCompleteEvent", 
    "TaskFailEvent",
    "StepStartEvent",
    "StepCompleteEvent"
]
