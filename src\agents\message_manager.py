"""
Enhanced message manager for agent communication.
Based on browser-use patterns for production reliability.
"""

import logging
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime
from pydantic import BaseModel, Field

from ..llm.messages import BaseMessage, SystemMessage, UserMessage, AssistantMessage, ContentPartTextParam, ContentPartImageParam
from ..utils.logging import get_logger

logger = get_logger(__name__)


class HistoryItem(BaseModel):
    """Single item in conversation history."""
    
    message: BaseMessage
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    tokens: Optional[int] = None
    cost: Optional[float] = None


class MessageManagerState(BaseModel):
    """State of the message manager."""
    
    conversation_history: List[HistoryItem] = Field(default_factory=list)
    system_message: Optional[SystemMessage] = None
    max_history_items: Optional[int] = None
    total_tokens: int = 0
    total_cost: float = 0.0


class MessageManager:
    """Manages conversation history and message formatting."""
    
    def __init__(
        self,
        max_history_items: Optional[int] = None,
        vision_detail_level: Literal['auto', 'low', 'high'] = 'auto'
    ):
        self.state = MessageManagerState(max_history_items=max_history_items)
        self.vision_detail_level = vision_detail_level
        self.logger = logger
    
    def set_system_message(self, message: SystemMessage):
        """Set the system message."""
        self.state.system_message = message
        self.logger.debug("System message set")
    
    def add_message(
        self,
        message: BaseMessage,
        tokens: Optional[int] = None,
        cost: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Add a message to the conversation history."""
        history_item = HistoryItem(
            message=message,
            tokens=tokens,
            cost=cost,
            metadata=metadata or {}
        )
        
        self.state.conversation_history.append(history_item)
        
        # Update totals
        if tokens:
            self.state.total_tokens += tokens
        if cost:
            self.state.total_cost += cost
        
        # Trim history if needed
        if (self.state.max_history_items and 
            len(self.state.conversation_history) > self.state.max_history_items):
            removed = self.state.conversation_history.pop(0)
            self.logger.debug(f"Removed oldest message from history: {removed.message.role}")
        
        self.logger.debug(f"Added {message.role} message to history")
    
    def get_messages_for_llm(self) -> List[BaseMessage]:
        """Get formatted messages for LLM consumption."""
        messages = []
        
        # Add system message if present
        if self.state.system_message:
            messages.append(self.state.system_message)
        
        # Add conversation history
        for item in self.state.conversation_history:
            messages.append(item.message)
        
        return messages
    
    def create_user_message(
        self,
        text: str,
        images: Optional[List[bytes]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> UserMessage:
        """Create a user message with optional images."""
        if not images:
            return UserMessage(content=text, metadata=metadata or {})
        
        # Create content parts
        content_parts = [ContentPartTextParam(text=text)]
        
        for image_data in images:
            # Convert to base64 and create image part
            import base64
            base64_image = base64.b64encode(image_data).decode('utf-8')
            data_url = f"data:image/png;base64,{base64_image}"
            
            content_parts.append(ContentPartImageParam(
                image_url={
                    "url": data_url,
                    "detail": self.vision_detail_level
                }
            ))
        
        return UserMessage(content=content_parts, metadata=metadata or {})
    
    def create_assistant_message(
        self,
        text: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AssistantMessage:
        """Create an assistant message."""
        return AssistantMessage(content=text, metadata=metadata or {})
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation."""
        return {
            "total_messages": len(self.state.conversation_history),
            "total_tokens": self.state.total_tokens,
            "total_cost": self.state.total_cost,
            "has_system_message": self.state.system_message is not None,
            "message_types": self._get_message_type_counts()
        }
    
    def _get_message_type_counts(self) -> Dict[str, int]:
        """Get counts of different message types."""
        counts = {}
        for item in self.state.conversation_history:
            msg_type = item.message.role
            counts[msg_type] = counts.get(msg_type, 0) + 1
        return counts
    
    def clear_history(self, keep_system_message: bool = True):
        """Clear conversation history."""
        self.state.conversation_history.clear()
        self.state.total_tokens = 0
        self.state.total_cost = 0.0
        
        if not keep_system_message:
            self.state.system_message = None
        
        self.logger.debug("Conversation history cleared")
    
    def export_conversation(self) -> List[Dict[str, Any]]:
        """Export conversation history for saving/analysis."""
        return [
            {
                "role": item.message.role,
                "content": str(item.message.content),
                "timestamp": item.timestamp.isoformat(),
                "tokens": item.tokens,
                "cost": item.cost,
                "metadata": item.metadata
            }
            for item in self.state.conversation_history
        ]
    
    def import_conversation(self, conversation_data: List[Dict[str, Any]]):
        """Import conversation history from exported data."""
        self.state.conversation_history.clear()
        
        for item_data in conversation_data:
            # Recreate message based on role
            role = item_data["role"]
            content = item_data["content"]
            
            if role == "system":
                message = SystemMessage(content=content)
            elif role == "user":
                message = UserMessage(content=content)
            elif role == "assistant":
                message = AssistantMessage(content=content)
            else:
                # Generic message
                message = BaseMessage(role=role, content=content)
            
            # Create history item
            history_item = HistoryItem(
                message=message,
                timestamp=datetime.fromisoformat(item_data["timestamp"]),
                tokens=item_data.get("tokens"),
                cost=item_data.get("cost"),
                metadata=item_data.get("metadata", {})
            )
            
            self.state.conversation_history.append(history_item)
        
        # Recalculate totals
        self.state.total_tokens = sum(item.tokens or 0 for item in self.state.conversation_history)
        self.state.total_cost = sum(item.cost or 0.0 for item in self.state.conversation_history)
        
        self.logger.debug(f"Imported {len(conversation_data)} messages")
    
    def get_recent_messages(self, count: int = 5) -> List[HistoryItem]:
        """Get the most recent messages."""
        return self.state.conversation_history[-count:] if self.state.conversation_history else []
    
    def find_messages_by_role(self, role: str) -> List[HistoryItem]:
        """Find all messages with a specific role."""
        return [item for item in self.state.conversation_history if item.message.role == role]
    
    def get_token_usage_stats(self) -> Dict[str, Any]:
        """Get detailed token usage statistics."""
        stats = {
            "total_tokens": self.state.total_tokens,
            "total_cost": self.state.total_cost,
            "average_tokens_per_message": 0,
            "by_role": {}
        }
        
        if self.state.conversation_history:
            total_messages = len(self.state.conversation_history)
            stats["average_tokens_per_message"] = self.state.total_tokens / total_messages
            
            # Calculate by role
            for item in self.state.conversation_history:
                role = item.message.role
                if role not in stats["by_role"]:
                    stats["by_role"][role] = {"count": 0, "tokens": 0, "cost": 0.0}
                
                stats["by_role"][role]["count"] += 1
                stats["by_role"][role]["tokens"] += item.tokens or 0
                stats["by_role"][role]["cost"] += item.cost or 0.0
        
        return stats
