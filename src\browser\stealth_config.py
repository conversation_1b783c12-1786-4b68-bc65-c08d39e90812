"""
Stealth configuration for avoiding bot detection.
"""

from typing import Dict, Any, List
import random

from ..utils import get_logger

logger = get_logger(__name__)


class StealthConfig:
    """Configuration for stealth mode to avoid bot detection."""
    
    def __init__(self):
        self.logger = get_logger("stealth_config")
        
        # Common user agents for rotation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        # Viewport sizes for rotation
        self.viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1440, "height": 900},
            {"width": 1536, "height": 864},
            {"width": 1280, "height": 720}
        ]
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent string."""
        return random.choice(self.user_agents)
    
    def get_random_viewport(self) -> Dict[str, int]:
        """Get a random viewport size."""
        return random.choice(self.viewports)
    
    def get_launch_options(self) -> Dict[str, Any]:
        """Get browser launch options for stealth mode."""
        return {
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-blink-features=AutomationControlled",
                "--disable-background-networking",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-default-apps",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--disable-translate",
                "--disable-web-security",
                "--metrics-recording-only",
                "--no-default-browser-check",
                "--safebrowsing-disable-auto-update",
                "--enable-automation=false",
                "--password-store=basic",
                "--use-mock-keychain"
            ]
        }
    
    def get_context_options(self) -> Dict[str, Any]:
        """Get browser context options for stealth mode."""
        return {
            "user_agent": self.get_random_user_agent(),
            "viewport": self.get_random_viewport(),
            "locale": "en-US",
            "timezone_id": "America/New_York",
            "permissions": ["geolocation"],
            "extra_http_headers": {
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-User": "?1",
                "Sec-Fetch-Dest": "document"
            }
        }
    
    async def apply_stealth_modifications(self, context):
        """Apply stealth modifications to the browser context."""
        try:
            # Add stealth scripts to all pages
            await context.add_init_script(self._get_stealth_script())
            
            # Set up request interception for additional stealth
            await context.route("**/*", self._handle_request)
            
            self.logger.debug("Applied stealth modifications")
            
        except Exception as e:
            self.logger.error(f"Failed to apply stealth modifications: {e}")
    
    def _get_stealth_script(self) -> str:
        """Get JavaScript code for stealth modifications."""
        return """
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Mock languages and plugins
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Mock permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // Hide automation indicators
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        
        // Mock chrome runtime
        if (!window.chrome) {
            window.chrome = {};
        }
        
        if (!window.chrome.runtime) {
            window.chrome.runtime = {
                onConnect: undefined,
                onMessage: undefined,
                sendMessage: undefined,
            };
        }
        
        // Override the `plugins` property to use a custom getter.
        Object.defineProperty(navigator, 'plugins', {
            get: function() {
                return [1, 2, 3, 4, 5];
            },
        });
        
        // Pass the Webdriver test
        Object.defineProperty(navigator, 'webdriver', {
            get: () => false,
        });
        
        // Pass the Chrome test
        window.chrome = {
            runtime: {},
        };
        
        // Pass the Permissions test
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // Overwrite the `plugins` property to use a custom getter.
        Object.defineProperty(navigator, 'plugins', {
            get: function() {
                return [
                    {
                        0: {
                            type: "application/x-google-chrome-pdf",
                            suffixes: "pdf",
                            description: "Portable Document Format",
                            enabledPlugin: Plugin,
                        },
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin",
                    },
                    {
                        0: {
                            type: "application/pdf",
                            suffixes: "pdf",
                            description: "",
                            enabledPlugin: Plugin,
                        },
                        description: "",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer",
                    },
                    {
                        0: {
                            type: "application/x-nacl",
                            suffixes: "",
                            description: "Native Client Executable",
                            enabledPlugin: Plugin,
                        },
                        1: {
                            type: "application/x-pnacl",
                            suffixes: "",
                            description: "Portable Native Client Executable",
                            enabledPlugin: Plugin,
                        },
                        description: "",
                        filename: "internal-nacl-plugin",
                        length: 2,
                        name: "Native Client",
                    },
                ];
            },
        });
        """
    
    async def _handle_request(self, route):
        """Handle requests for additional stealth measures."""
        try:
            import asyncio
            request = route.request

            # Add random delays to mimic human behavior
            if random.random() < 0.1:  # 10% chance of delay
                delay = random.uniform(0.1, 0.5)
                await asyncio.sleep(delay)
            
            # Modify headers for stealth
            headers = dict(request.headers)
            
            # Remove automation headers
            headers.pop("sec-ch-ua", None)
            headers.pop("sec-ch-ua-mobile", None)
            headers.pop("sec-ch-ua-platform", None)
            
            # Add realistic headers
            if "Referer" not in headers and request.url != request.frame.url:
                headers["Referer"] = request.frame.url
            
            await route.continue_(headers=headers)
            
        except Exception as e:
            self.logger.error(f"Error handling request: {e}")
            await route.continue_()
    
    def get_human_like_delays(self) -> Dict[str, float]:
        """Get human-like delays for various actions."""
        return {
            "typing_delay": random.uniform(0.05, 0.15),  # Delay between keystrokes
            "click_delay": random.uniform(0.1, 0.3),     # Delay before clicking
            "scroll_delay": random.uniform(0.2, 0.5),    # Delay between scroll actions
            "navigation_delay": random.uniform(1.0, 3.0) # Delay after navigation
        }
    
    def should_add_random_mouse_movement(self) -> bool:
        """Determine if random mouse movement should be added."""
        return random.random() < 0.3  # 30% chance
    
    def get_random_mouse_position(self, viewport: Dict[str, int]) -> Dict[str, int]:
        """Get a random mouse position within the viewport."""
        return {
            "x": random.randint(50, viewport["width"] - 50),
            "y": random.randint(50, viewport["height"] - 50)
        }
