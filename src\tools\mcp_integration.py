"""
Model Context Protocol (MCP) integration for extending agent capabilities.
"""

import async<PERSON>
import json
from typing import Dict, Any, Optional, List, Callable
import httpx

from ..utils import get_logger, config, NetworkError, retry_on_exception


class MCPIntegration:
    """Integration with Model Context Protocol servers."""
    
    def __init__(self, server_url: str = None):
        self.server_url = server_url or config.mcp.mcp_server_url
        self.logger = get_logger(__name__)
        self.client = httpx.AsyncClient(timeout=30.0)
        
        # Available tools from MCP server
        self.available_tools = {}
        self.tool_schemas = {}
        
        # Connection status
        self.is_connected = False
        self.server_info = {}
    
    async def initialize(self) -> bool:
        """Initialize connection to MCP server."""
        if not config.mcp.enable_mcp:
            self.logger.info("MCP integration is disabled")
            return False
        
        try:
            self.logger.info(f"Connecting to MCP server: {self.server_url}")
            
            # Test connection and get server info
            response = await self.client.get(f"{self.server_url}/info")
            
            if response.status_code == 200:
                self.server_info = response.json()
                self.is_connected = True
                
                # Load available tools
                await self._load_available_tools()
                
                self.logger.info(f"Connected to MCP server: {self.server_info.get('name', 'Unknown')}")
                return True
            else:
                self.logger.error(f"MCP server connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server: {e}")
            return False
    
    async def _load_available_tools(self):
        """Load available tools from the MCP server."""
        try:
            response = await self.client.get(f"{self.server_url}/tools")
            
            if response.status_code == 200:
                tools_data = response.json()
                
                for tool in tools_data.get("tools", []):
                    tool_name = tool.get("name")
                    if tool_name:
                        self.available_tools[tool_name] = tool
                        self.tool_schemas[tool_name] = tool.get("inputSchema", {})
                
                self.logger.info(f"Loaded {len(self.available_tools)} tools from MCP server")
            
        except Exception as e:
            self.logger.error(f"Failed to load MCP tools: {e}")
    
    def get_available_tools(self) -> List[str]:
        """Get list of available MCP tools."""
        return list(self.available_tools.keys())
    
    def get_tool_schema(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get schema for a specific tool."""
        return self.tool_schemas.get(tool_name)
    
    @retry_on_exception((NetworkError, httpx.RequestError))
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call a tool on the MCP server."""
        if not self.is_connected:
            raise NetworkError("Not connected to MCP server")
        
        if tool_name not in self.available_tools:
            raise ValueError(f"Tool '{tool_name}' not available on MCP server")
        
        try:
            self.logger.debug(f"Calling MCP tool: {tool_name}")
            
            payload = {
                "name": tool_name,
                "arguments": parameters or {}
            }
            
            response = await self.client.post(
                f"{self.server_url}/tools/call",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                self.logger.debug(f"MCP tool call successful: {tool_name}")
                return result
            else:
                error_msg = f"MCP tool call failed: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                raise NetworkError(error_msg)
                
        except httpx.RequestError as e:
            self.logger.error(f"Network error calling MCP tool {tool_name}: {e}")
            raise NetworkError(f"Network error calling MCP tool {tool_name}: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error calling MCP tool {tool_name}: {e}")
            raise
    
    async def get_resources(self) -> List[Dict[str, Any]]:
        """Get available resources from the MCP server."""
        if not self.is_connected:
            return []
        
        try:
            response = await self.client.get(f"{self.server_url}/resources")
            
            if response.status_code == 200:
                resources_data = response.json()
                return resources_data.get("resources", [])
            
        except Exception as e:
            self.logger.error(f"Failed to get MCP resources: {e}")
        
        return []
    
    async def read_resource(self, resource_uri: str) -> Optional[Dict[str, Any]]:
        """Read a specific resource from the MCP server."""
        if not self.is_connected:
            return None
        
        try:
            payload = {"uri": resource_uri}
            
            response = await self.client.post(
                f"{self.server_url}/resources/read",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            
        except Exception as e:
            self.logger.error(f"Failed to read MCP resource {resource_uri}: {e}")
        
        return None
    
    async def subscribe_to_resource(self, resource_uri: str, callback: Callable) -> bool:
        """Subscribe to resource updates (if supported by server)."""
        if not self.is_connected:
            return False
        
        try:
            payload = {"uri": resource_uri}
            
            response = await self.client.post(
                f"{self.server_url}/resources/subscribe",
                json=payload
            )
            
            if response.status_code == 200:
                # In a real implementation, you'd set up a WebSocket or SSE connection
                # for receiving updates and call the callback when updates arrive
                self.logger.info(f"Subscribed to resource: {resource_uri}")
                return True
            
        except Exception as e:
            self.logger.error(f"Failed to subscribe to resource {resource_uri}: {e}")
        
        return False
    
    async def send_prompt(self, prompt: str, context: Dict[str, Any] = None) -> Optional[str]:
        """Send a prompt to the MCP server for processing."""
        if not self.is_connected:
            return None
        
        try:
            payload = {
                "prompt": prompt,
                "context": context or {}
            }
            
            response = await self.client.post(
                f"{self.server_url}/prompts/process",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response")
            
        except Exception as e:
            self.logger.error(f"Failed to send prompt to MCP server: {e}")
        
        return None
    
    async def get_completion(self, messages: List[Dict[str, str]], tools: List[str] = None) -> Optional[Dict[str, Any]]:
        """Get completion from MCP server with optional tool use."""
        if not self.is_connected:
            return None
        
        try:
            payload = {
                "messages": messages,
                "tools": tools or []
            }
            
            response = await self.client.post(
                f"{self.server_url}/completion",
                json=payload
            )
            
            if response.status_code == 200:
                return response.json()
            
        except Exception as e:
            self.logger.error(f"Failed to get completion from MCP server: {e}")
        
        return None
    
    async def health_check(self) -> bool:
        """Check if the MCP server is healthy."""
        try:
            response = await self.client.get(f"{self.server_url}/health")
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"MCP server health check failed: {e}")
            return False
    
    async def get_server_capabilities(self) -> Dict[str, Any]:
        """Get server capabilities and supported features."""
        if not self.is_connected:
            return {}
        
        try:
            response = await self.client.get(f"{self.server_url}/capabilities")
            
            if response.status_code == 200:
                return response.json()
            
        except Exception as e:
            self.logger.error(f"Failed to get server capabilities: {e}")
        
        return {}
    
    async def register_client_capability(self, capability: str, config: Dict[str, Any] = None) -> bool:
        """Register a client capability with the server."""
        if not self.is_connected:
            return False
        
        try:
            payload = {
                "capability": capability,
                "config": config or {}
            }
            
            response = await self.client.post(
                f"{self.server_url}/capabilities/register",
                json=payload
            )
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Failed to register capability {capability}: {e}")
            return False
    
    async def close(self):
        """Close the MCP client connection."""
        try:
            if self.is_connected:
                # Send disconnect message if server supports it
                try:
                    await self.client.post(f"{self.server_url}/disconnect")
                except:
                    pass
                
                self.is_connected = False
            
            await self.client.aclose()
            self.logger.info("MCP client connection closed")
            
        except Exception as e:
            self.logger.error(f"Error closing MCP client: {e}")
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            asyncio.create_task(self.close())
        except:
            pass
