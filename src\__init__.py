"""
AI Sourcing Agent - Enhanced browser automation system.
Based on browser-use patterns for production reliability.
"""

import os
import logging
from typing import TYPE_CHECKING

# Set up logging if not in MCP mode or if explicitly requested
if os.environ.get('AI_SOURCING_SETUP_LOGGING', 'true').lower() != 'false':
    from .utils.logging import setup_logging
    
    # Get log file paths from config/environment
    debug_log_file = os.environ.get('AI_SOURCING_DEBUG_LOG_FILE')
    info_log_file = os.environ.get('AI_SOURCING_INFO_LOG_FILE')
    
    # Set up logging with file handlers if specified
    logger = setup_logging(debug_log_file=debug_log_file, info_log_file=info_log_file)
else:
    logger = logging.getLogger('ai_sourcing_agent')

# Type stubs for lazy imports - fixes linter warnings
if TYPE_CHECKING:
    from .agents.enhanced_agent import EnhancedAgent
    from .agents.web_agent import WebAgent
    from .browser.enhanced_browser import EnhancedBrowserSession
    from .browser.browser_manager import Browser<PERSON>anager
    from .browser.session_manager import SessionManager
    from .llm.enhanced_llm import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ider
    from .llm.llm_factory import create_llm
    from .tools.enhanced_tools import BrowserTools
    from .utils.enhanced_config import ENHANCED_CONFIG

# Lazy imports mapping - only import when actually accessed
_LAZY_IMPORTS = {
    # Agent service (heavy due to dependencies)
    'EnhancedAgent': ('src.agents.enhanced_agent', 'EnhancedAgent'),
    'WebAgent': ('src.agents.web_agent', 'WebAgent'),
    
    # Browser components
    'EnhancedBrowserSession': ('src.browser.enhanced_browser', 'EnhancedBrowserSession'),
    'BrowserManager': ('src.browser.browser_manager', 'BrowserManager'),
    'SessionManager': ('src.browser.session_manager', 'SessionManager'),
    
    # LLM components
    'EnhancedBaseLLMProvider': ('src.llm.enhanced_llm', 'EnhancedBaseLLMProvider'),
    'create_llm': ('src.llm.llm_factory', 'create_llm'),
    
    # Tools
    'BrowserTools': ('src.tools.enhanced_tools', 'BrowserTools'),
    
    # Configuration
    'ENHANCED_CONFIG': ('src.utils.enhanced_config', 'ENHANCED_CONFIG'),
}

def __getattr__(name: str):
    """Lazy import mechanism - only import modules when they're actually accessed."""
    if name in _LAZY_IMPORTS:
        module_path, attr_name = _LAZY_IMPORTS[name]
        try:
            from importlib import import_module
            
            module = import_module(module_path)
            if attr_name is None:
                # For modules, return the module itself
                attr = module
            else:
                attr = getattr(module, attr_name)
            # Cache the imported attribute in the module's globals
            globals()[name] = attr
            return attr
        except ImportError as e:
            raise ImportError(f'Failed to import {name} from {module_path}: {e}') from e
    
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__version__ = "2.0.0"

__all__ = [
    'EnhancedAgent',
    'WebAgent',
    'EnhancedBrowserSession', 
    'BrowserManager',
    'SessionManager',
    'EnhancedBaseLLMProvider',
    'create_llm',
    'BrowserTools',
    'ENHANCED_CONFIG',
]
