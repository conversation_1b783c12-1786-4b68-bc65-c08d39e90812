"""
LLM Factory for creating and managing different LLM providers.
"""

from typing import Optional, Dict, Any, Union
from abc import ABC, abstractmethod

from ..utils import config, get_logger, LLMError

logger = get_logger(__name__)


class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, api_key: str, model: str = None, **kwargs):
        self.api_key = api_key
        self.model = model
        self.config = kwargs
        self.logger = get_logger(f"llm.{self.__class__.__name__.lower()}")
    
    @abstractmethod
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate a response from the LLM."""
        pass
    
    @abstractmethod
    async def generate_structured_response(self, prompt: str, schema: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Generate a structured response from the LLM."""
        pass
    
    @abstractmethod
    def validate_configuration(self) -> bool:
        """Validate the provider configuration."""
        pass


class LLMFactory:
    """Factory class for creating LLM provider instances."""
    
    _providers = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class: type):
        """Register a new LLM provider."""
        cls._providers[name] = provider_class
        logger.info(f"Registered LLM provider: {name}")
    
    @classmethod
    def create_provider(cls, provider_name: str, **kwargs) -> BaseLLMProvider:
        """Create an LLM provider instance."""
        if provider_name not in cls._providers:
            available = ", ".join(cls._providers.keys())
            raise LLMError(f"Unknown provider '{provider_name}'. Available: {available}")
        
        provider_class = cls._providers[provider_name]
        
        # Get configuration from config if not provided
        llm_config = config.get_llm_config(provider_name)
        
        # Merge provided kwargs with config
        final_config = {**llm_config, **kwargs}
        
        try:
            provider = provider_class(**final_config)
            provider.validate_configuration()
            logger.info(f"Created LLM provider: {provider_name}")
            return provider
        except Exception as e:
            logger.error(f"Failed to create provider {provider_name}: {e}")
            raise LLMError(f"Failed to create provider {provider_name}: {e}")
    
    @classmethod
    def get_available_providers(cls) -> list:
        """Get list of available provider names."""
        return list(cls._providers.keys())


def create_llm(provider: str = None, model: str = None, **kwargs) -> BaseLLMProvider:
    """
    Convenience function to create an LLM provider.
    
    Args:
        provider: Provider name (defaults to config default)
        model: Model name (defaults to config default)
        **kwargs: Additional configuration
    
    Returns:
        BaseLLMProvider: Configured LLM provider instance
    """
    if provider is None:
        provider = config.llm.default_provider
    
    if model is None:
        model = config.llm.default_model
    
    return LLMFactory.create_provider(provider, model=model, **kwargs)


# Import and register providers
try:
    from .providers.gemini_provider import GeminiProvider
    LLMFactory.register_provider("gemini", GeminiProvider)
except ImportError as e:
    logger.warning(f"Could not register Gemini provider: {e}")

try:
    from .providers.openai_provider import OpenAIProvider
    LLMFactory.register_provider("openai", OpenAIProvider)
except ImportError as e:
    logger.warning(f"Could not register OpenAI provider: {e}")

try:
    from .providers.anthropic_provider import AnthropicProvider
    LLMFactory.register_provider("anthropic", AnthropicProvider)
except ImportError as e:
    logger.warning(f"Could not register Anthropic provider: {e}")
