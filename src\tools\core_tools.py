"""
Core browser automation tools.
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

from ..utils import get_logger, ElementNotFoundError, TimeoutError, retry_on_exception, safe_execute_async


class BrowserTools:
    """Core browser automation tools."""
    
    def __init__(self, browser_manager, logger=None):
        self.browser_manager = browser_manager
        self.logger = logger or get_logger(__name__)
        self.is_initialized = False
        
        # Tool configuration
        self.default_timeout = 10000
        self.retry_attempts = 3
        self.human_delays = True
    
    async def initialize(self) -> bool:
        """Initialize the browser tools."""
        try:
            if not self.browser_manager.is_initialized:
                await self.browser_manager.initialize()
            
            self.is_initialized = True
            self.logger.debug("Browser tools initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize browser tools: {e}")
            return False
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool methods."""
        return [
            "click_element",
            "type_text",
            "select_option",
            "scroll_page",
            "wait_for_element",
            "get_element_text",
            "get_element_attribute",
            "take_screenshot",
            "navigate_to",
            "go_back",
            "go_forward",
            "refresh_page",
            "switch_tab",
            "close_tab",
            "upload_file",
            "download_file",
            "execute_javascript",
            "wait_for_page_load"
        ]
    
    async def _add_human_delay(self, action_type: str = "default"):
        """Add human-like delays to actions."""
        if not self.human_delays:
            return
        
        delays = {
            "click": (0.1, 0.3),
            "type": (0.05, 0.15),
            "scroll": (0.2, 0.5),
            "navigation": (1.0, 2.0),
            "default": (0.1, 0.2)
        }
        
        min_delay, max_delay = delays.get(action_type, delays["default"])
        delay = min_delay + (max_delay - min_delay) * time.time() % 1
        await asyncio.sleep(delay)
    
    @retry_on_exception((ElementNotFoundError, TimeoutError))
    async def click_element(self, selector: str, timeout: int = None) -> bool:
        """Click on an element."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout
            
            self.logger.debug(f"Clicking element: {selector}")
            
            # Wait for element to be visible and clickable
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state="visible"
            )
            
            if not element:
                raise ElementNotFoundError(f"Element not found: {selector}")
            
            # Add human-like delay
            await self._add_human_delay("click")
            
            # Scroll element into view if needed
            await element.scroll_into_view_if_needed()
            
            # Click the element
            await element.click()
            
            self.logger.debug(f"Successfully clicked: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to click element {selector}: {e}")
            raise ElementNotFoundError(f"Failed to click element {selector}: {e}")
    
    @retry_on_exception((ElementNotFoundError, TimeoutError))
    async def type_text(self, selector: str, text: str, timeout: int = None, clear_first: bool = True) -> bool:
        """Type text into an element."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout
            
            self.logger.debug(f"Typing text into: {selector}")
            
            # Wait for element
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state="visible"
            )
            
            if not element:
                raise ElementNotFoundError(f"Element not found: {selector}")
            
            # Scroll into view
            await element.scroll_into_view_if_needed()
            
            # Clear existing text if requested
            if clear_first:
                await element.click()
                await page.keyboard.press("Control+a")
                await page.keyboard.press("Delete")
            
            # Add human-like delay
            await self._add_human_delay("type")
            
            # Type the text with human-like typing speed
            if self.human_delays:
                for char in text:
                    await page.keyboard.type(char)
                    await asyncio.sleep(0.05 + 0.1 * time.time() % 1)
            else:
                await element.type(text)
            
            self.logger.debug(f"Successfully typed text into: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to type text into {selector}: {e}")
            raise ElementNotFoundError(f"Failed to type text into {selector}: {e}")
    
    @retry_on_exception((ElementNotFoundError, TimeoutError))
    async def select_option(self, selector: str, value: str, timeout: int = None) -> bool:
        """Select an option from a dropdown."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout
            
            self.logger.debug(f"Selecting option '{value}' from: {selector}")
            
            # Wait for select element
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state="visible"
            )
            
            if not element:
                raise ElementNotFoundError(f"Select element not found: {selector}")
            
            # Scroll into view
            await element.scroll_into_view_if_needed()
            
            # Add human-like delay
            await self._add_human_delay("click")
            
            # Select the option
            await element.select_option(value=value)
            
            self.logger.debug(f"Successfully selected option '{value}' from: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to select option from {selector}: {e}")
            raise ElementNotFoundError(f"Failed to select option from {selector}: {e}")
    
    async def scroll_page(self, direction: str = "down", amount: int = 500) -> bool:
        """Scroll the page."""
        try:
            page = await self.browser_manager.get_page()
            
            self.logger.debug(f"Scrolling page {direction} by {amount}px")
            
            # Add human-like delay
            await self._add_human_delay("scroll")
            
            if direction.lower() == "down":
                await page.evaluate(f"window.scrollBy(0, {amount})")
            elif direction.lower() == "up":
                await page.evaluate(f"window.scrollBy(0, -{amount})")
            elif direction.lower() == "left":
                await page.evaluate(f"window.scrollBy(-{amount}, 0)")
            elif direction.lower() == "right":
                await page.evaluate(f"window.scrollBy({amount}, 0)")
            elif direction.lower() == "top":
                await page.evaluate("window.scrollTo(0, 0)")
            elif direction.lower() == "bottom":
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            
            self.logger.debug(f"Successfully scrolled page {direction}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to scroll page: {e}")
            return False
    
    async def wait_for_element(self, selector: str, timeout: int = None, state: str = "visible") -> bool:
        """Wait for an element to appear."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout
            
            self.logger.debug(f"Waiting for element: {selector}")
            
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state=state
            )
            
            return element is not None
            
        except Exception as e:
            self.logger.error(f"Element wait timeout: {selector} - {e}")
            return False
    
    async def get_element_text(self, selector: str, timeout: int = None) -> Optional[str]:
        """Get text content of an element."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout
            
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state="visible"
            )
            
            if element:
                text = await element.text_content()
                self.logger.debug(f"Got text from {selector}: {text[:100]}...")
                return text
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get text from {selector}: {e}")
            return None
    
    async def get_element_attribute(self, selector: str, attribute: str, timeout: int = None) -> Optional[str]:
        """Get an attribute value from an element."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout
            
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state="attached"
            )
            
            if element:
                value = await element.get_attribute(attribute)
                self.logger.debug(f"Got attribute {attribute} from {selector}: {value}")
                return value
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get attribute {attribute} from {selector}: {e}")
            return None
    
    async def take_screenshot(self, filename: str = None, full_page: bool = True) -> Optional[str]:
        """Take a screenshot of the current page."""
        try:
            page = await self.browser_manager.get_page()
            screenshot_path = await self.browser_manager.take_screenshot(page, filename)
            
            if screenshot_path:
                self.logger.debug(f"Screenshot saved: {screenshot_path}")
            
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            return None
    
    async def navigate_to(self, url: str) -> bool:
        """Navigate to a URL."""
        try:
            self.logger.debug(f"Navigating to: {url}")
            
            # Add human-like delay
            await self._add_human_delay("navigation")
            
            success = await self.browser_manager.navigate(url)
            
            if success:
                self.logger.debug(f"Successfully navigated to: {url}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to {url}: {e}")
            return False
    
    async def go_back(self) -> bool:
        """Go back in browser history."""
        try:
            page = await self.browser_manager.get_page()
            await page.go_back(wait_until="domcontentloaded")
            
            self.logger.debug("Navigated back")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to go back: {e}")
            return False
    
    async def go_forward(self) -> bool:
        """Go forward in browser history."""
        try:
            page = await self.browser_manager.get_page()
            await page.go_forward(wait_until="domcontentloaded")
            
            self.logger.debug("Navigated forward")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to go forward: {e}")
            return False
    
    async def refresh_page(self) -> bool:
        """Refresh the current page."""
        try:
            page = await self.browser_manager.get_page()
            await page.reload(wait_until="domcontentloaded")
            
            self.logger.debug("Page refreshed")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to refresh page: {e}")
            return False
    
    async def execute_javascript(self, script: str) -> Any:
        """Execute JavaScript on the page."""
        try:
            page = await self.browser_manager.get_page()
            result = await page.evaluate(script)
            
            self.logger.debug(f"Executed JavaScript: {script[:100]}...")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to execute JavaScript: {e}")
            return None
    
    async def wait_for_page_load(self, timeout: int = None) -> bool:
        """Wait for page to finish loading."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout

            await page.wait_for_load_state("networkidle", timeout=timeout)

            self.logger.debug("Page load completed")
            return True

        except Exception as e:
            self.logger.error(f"Page load timeout: {e}")
            return False

    async def upload_file(self, selector: str, file_path: str, timeout: int = None) -> bool:
        """Upload a file to a file input element."""
        try:
            page = await self.browser_manager.get_page()
            timeout = timeout or self.default_timeout

            self.logger.debug(f"Uploading file {file_path} to: {selector}")

            # Wait for file input element
            element = await page.wait_for_selector(
                selector,
                timeout=timeout,
                state="attached"
            )

            if not element:
                raise ElementNotFoundError(f"File input not found: {selector}")

            # Check if file exists
            if not Path(file_path).exists():
                raise FileNotFoundError(f"File not found: {file_path}")

            # Upload the file
            await element.set_input_files(file_path)

            self.logger.debug(f"Successfully uploaded file: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to upload file: {e}")
            return False

    async def switch_tab(self, tab_index: int = None, tab_url: str = None) -> bool:
        """Switch to a different browser tab."""
        try:
            context = self.browser_manager.context
            pages = context.pages

            if tab_index is not None:
                if 0 <= tab_index < len(pages):
                    await pages[tab_index].bring_to_front()
                    self.browser_manager.page = pages[tab_index]
                    self.logger.debug(f"Switched to tab {tab_index}")
                    return True
            elif tab_url:
                for page in pages:
                    if tab_url in page.url:
                        await page.bring_to_front()
                        self.browser_manager.page = page
                        self.logger.debug(f"Switched to tab with URL: {tab_url}")
                        return True

            return False

        except Exception as e:
            self.logger.error(f"Failed to switch tab: {e}")
            return False

    async def close_tab(self, tab_index: int = None) -> bool:
        """Close a browser tab."""
        try:
            context = self.browser_manager.context
            pages = context.pages

            if tab_index is not None and 0 <= tab_index < len(pages):
                await pages[tab_index].close()
                self.logger.debug(f"Closed tab {tab_index}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Failed to close tab: {e}")
            return False
