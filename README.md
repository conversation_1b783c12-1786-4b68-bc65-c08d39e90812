# Browser Automation System

A comprehensive browser automation system built on the browser-use library, featuring AI-powered web interactions, multi-LLM support, and advanced automation capabilities.

## 🚀 Features

### Core Capabilities
- **AI-Powered Automation**: Natural language task execution using advanced LLMs
- **Multi-LLM Support**: Google Gemini, OpenAI GPT, and Anthropic Claude integration
- **Real Browser Control**: Uses Playwright for authentic browser interactions
- **Session Management**: Save, load, and restore browser sessions
- **Stealth Mode**: Anti-detection techniques to avoid bot detection
- **Advanced Tools**: Form filling, data extraction, table parsing, infinite scroll handling

### LLM Providers
- **Google Gemini**: Default provider with structured response support
- **OpenAI GPT**: Chat completions with function calling
- **Anthropic Claude**: Message-based API with tool support

### Browser Features
- **Session Persistence**: Save and restore browser states
- **Screenshot Capture**: Automatic screenshot generation for debugging
- **Cookie Management**: Advanced cookie handling and persistence
- **Performance Monitoring**: Track page load times and resource usage
- **Accessibility Checking**: Basic accessibility issue detection

### Advanced Tools
- **Smart Form Filling**: Intelligent field detection and data mapping
- **Table Extraction**: Extract structured data from HTML tables
- **Infinite Scroll**: Handle dynamic content loading
- **AJAX Handling**: Wait for asynchronous requests to complete
- **Element Highlighting**: Visual debugging with element highlighting
- **MCP Integration**: Model Context Protocol support for extensibility

## 📦 Installation

### Prerequisites
- Python 3.11 or higher
- Chrome/Chromium browser
- API keys for your chosen LLM provider

### Quick Setup

1. **Clone the repository**:
```bash
git clone https://github.com/your-org/browser-automation-system.git
cd browser-automation-system
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Install Playwright browsers**:
```bash
playwright install chromium
```

4. **Set up environment variables**:
```bash
cp .env.example .env
# Edit .env with your API keys
```

5. **Run the system**:
```bash
python main.py
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# LLM Configuration
GOOGLE_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Default LLM Settings
DEFAULT_LLM_PROVIDER=gemini
DEFAULT_LLM_MODEL=gemini-pro

# Browser Configuration
BROWSER_HEADLESS=false
BROWSER_TIMEOUT=30000
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080

# Logging Configuration
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_SCREENSHOTS=true

# Performance Settings
MAX_CONCURRENT_PAGES=5
REQUEST_TIMEOUT=30000
RETRY_ATTEMPTS=3

# MCP Integration (Optional)
ENABLE_MCP=false
MCP_SERVER_URL=http://localhost:8000
```

## 🎯 Usage

### Interactive Mode

Run the system in interactive mode for real-time task execution:

```bash
python main.py
```

Example interactions:
```
🔤 Enter task: Navigate to https://example.com
🔤 Enter task: Click on the "About" link
🔤 Enter task: Fill out the contact form with my information
🔤 Enter task: Extract all product names from this page
```

### Single Task Mode

Execute a single task:

```bash
python main.py --task "Navigate to google.com and search for 'browser automation'"
```

### Script Mode

Run multiple tasks from a file:

```bash
python main.py --script tasks.txt
```

Example `tasks.txt`:
```
Navigate to https://httpbin.org
Click on the "Forms" link
Fill out the form with sample data
Take a screenshot
Extract any visible data
```

### Examples Mode

Run built-in examples:

```bash
python main.py --examples
```

## 🧠 Programming Interface

### Basic Usage

```python
import asyncio
from src.agents import WebAgent

async def main():
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a website
        result = await agent.run("Navigate to https://example.com")
        print(result)
        
        # Interact with elements
        result = await agent.run("Click on the login button")
        print(result)
        
        # Fill forms
        result = await agent.run(
            "Fill out the contact form",
            form_data={
                "name": "John Doe",
                "email": "<EMAIL>",
                "message": "Hello world!"
            }
        )
        print(result)

if __name__ == "__main__":
    asyncio.run(main())
```

### Advanced Features

```python
from src.agents import WebAgent
from src.tools import CustomTools

async def advanced_example():
    async with WebAgent() as agent:
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools)
        
        # Smart form filling
        form_data = {"name": "Jane", "email": "<EMAIL>"}
        result = await custom_tools.smart_form_filler(form_data)
        
        # Extract table data
        tables = await custom_tools.table_extractor("table.data")
        
        # Handle infinite scroll
        scroll_result = await custom_tools.infinite_scroll_handler(max_scrolls=5)
        
        # Performance monitoring
        perf_metrics = await custom_tools.performance_monitor()
```

## 📊 Examples

### Basic Automation
- Navigation and page interaction
- Form filling and submission
- Data extraction from web pages
- Element clicking and text input
- Screenshot capture

### Advanced Features
- Smart form filling with field detection
- Table data extraction and parsing
- Infinite scroll handling
- Cookie management
- Performance monitoring
- Accessibility checking

## 🛠️ Development

### Project Structure

```
browser-automation-system/
├── src/
│   ├── agents/          # Agent implementations
│   ├── browser/         # Browser management
│   ├── llm/            # LLM providers and integration
│   ├── tools/          # Automation tools
│   ├── utils/          # Utilities and configuration
│   └── examples/       # Example scripts
├── tests/              # Test suite
├── config/             # Configuration files
├── logs/               # Log files and screenshots
├── main.py            # Main entry point
├── requirements.txt   # Dependencies
└── README.md         # This file
```

### Running Tests

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html
```

### Code Quality

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## 🔍 Troubleshooting

### Common Issues

1. **Browser not launching**:
   - Ensure Playwright browsers are installed: `playwright install chromium`
   - Check if Chrome/Chromium is available on your system

2. **API key errors**:
   - Verify your API keys are correctly set in the `.env` file
   - Check that the API keys have the necessary permissions

3. **Element not found errors**:
   - The page might still be loading - increase timeouts
   - Elements might be in iframes or shadow DOM
   - Use element highlighting for debugging

4. **Performance issues**:
   - Reduce concurrent pages in configuration
   - Enable headless mode for better performance
   - Adjust timeout values based on your network

### Debug Mode

Enable debug logging for detailed information:

```bash
python main.py --debug
```

## 📚 Documentation

- [API Reference](docs/api.md)
- [Configuration Guide](docs/configuration.md)
- [Examples and Tutorials](docs/examples.md)
- [Troubleshooting Guide](docs/troubleshooting.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built on the excellent [browser-use](https://github.com/browser-use/browser-use) library
- Powered by [Playwright](https://playwright.dev/) for browser automation
- Supports multiple LLM providers for maximum flexibility

## 📞 Support

- GitHub Issues: [Report bugs and request features](https://github.com/your-org/browser-automation-system/issues)
- Documentation: [Read the full documentation](https://browser-automation-system.readthedocs.io/)
- Community: [Join our discussions](https://github.com/your-org/browser-automation-system/discussions)

---

**Happy Automating! 🤖✨**
