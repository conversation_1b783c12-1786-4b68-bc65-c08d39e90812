"""
Enhanced message system for LLM interactions.
Based on browser-use patterns for structured communication.
"""

from typing import Literal, Union, List, Optional, Any, Dict
from pydantic import BaseModel, Field
import base64


def _truncate(text: str, max_length: int = 50) -> str:
    """Truncate text to max_length characters, adding ellipsis if truncated."""
    if len(text) <= max_length:
        return text
    return text[: max_length - 3] + '...'


def _format_image_url(url: str, max_length: int = 50) -> str:
    """Format image URL for display, truncating if necessary."""
    if url.startswith('data:'):
        # Base64 image
        media_type = url.split(';')[0].split(':')[1] if ';' in url else 'image'
        return f'<base64 {media_type}>'
    else:
        # Regular URL
        return _truncate(url, max_length)


class ContentPartTextParam(BaseModel):
    """Text content part for messages."""
    text: str
    type: Literal['text'] = 'text'

    def __str__(self) -> str:
        return f'Text: {_truncate(self.text)}'

    def __repr__(self) -> str:
        return f'ContentPartTextParam(text={_truncate(self.text)})'


class ContentPartRefusalParam(BaseModel):
    """Refusal content part for messages."""
    refusal: str
    type: Literal['refusal'] = 'refusal'

    def __str__(self) -> str:
        return f'Refusal: {_truncate(self.refusal)}'

    def __repr__(self) -> str:
        return f'ContentPartRefusalParam(refusal={_truncate(repr(self.refusal), 50)})'


SupportedImageMediaType = Literal['image/jpeg', 'image/png', 'image/gif', 'image/webp']


class ImageURL(BaseModel):
    """Image URL with metadata."""
    url: str
    """Either a URL of the image or the base64 encoded image data."""
    detail: Literal['auto', 'low', 'high'] = 'auto'
    """Specifies the detail level of the image."""
    media_type: SupportedImageMediaType = 'image/png'
    """Media type for the image."""

    def __str__(self) -> str:
        url_display = _format_image_url(self.url)
        return f'🖼️  Image[{self.media_type}, detail={self.detail}]: {url_display}'

    def __repr__(self) -> str:
        url_repr = _format_image_url(self.url, 30)
        return f'ImageURL(url={repr(url_repr)}, detail={repr(self.detail)}, media_type={repr(self.media_type)})'


class ContentPartImageParam(BaseModel):
    """Image content part for messages."""
    image_url: ImageURL
    type: Literal['image_url'] = 'image_url'

    def __str__(self) -> str:
        return str(self.image_url)

    def __repr__(self) -> str:
        return f'ContentPartImageParam(image_url={repr(self.image_url)})'


class Function(BaseModel):
    """Function call representation."""
    arguments: str
    """The arguments to call the function with, as generated by the model in JSON format."""
    name: str
    """The name of the function to call."""

    def __str__(self) -> str:
        args_preview = _truncate(self.arguments, 80)
        return f'{self.name}({args_preview})'

    def __repr__(self) -> str:
        return f'Function(name={repr(self.name)}, arguments={_truncate(repr(self.arguments), 50)})'


class ToolCall(BaseModel):
    """Tool call representation."""
    id: str
    function: Function
    type: Literal['function'] = 'function'

    def __str__(self) -> str:
        return f'ToolCall[{self.id[:8]}...]: {self.function}'

    def __repr__(self) -> str:
        return f'ToolCall(id={repr(self.id)}, function={repr(self.function)})'


class ContentPartToolCallParam(BaseModel):
    """Tool call content part for messages."""
    tool_calls: List[ToolCall]
    type: Literal['tool_calls'] = 'tool_calls'

    def __str__(self) -> str:
        calls_str = ', '.join(str(call) for call in self.tool_calls[:2])
        if len(self.tool_calls) > 2:
            calls_str += f', ... ({len(self.tool_calls)} total)'
        return f'ToolCalls: {calls_str}'

    def __repr__(self) -> str:
        return f'ContentPartToolCallParam(tool_calls={repr(self.tool_calls)})'


# Union type for all content parts
ContentPart = Union[
    ContentPartTextParam,
    ContentPartImageParam,
    ContentPartRefusalParam,
    ContentPartToolCallParam
]


class BaseMessage(BaseModel):
    """Base class for all message types."""
    role: str
    content: Union[str, List[ContentPart]]
    cache: bool = False
    metadata: Dict[str, Any] = Field(default_factory=dict)

    def __str__(self) -> str:
        if isinstance(self.content, str):
            content_preview = _truncate(self.content, 100)
        else:
            content_preview = f'{len(self.content)} parts'
        return f'{self.role.title()}: {content_preview}'

    def __repr__(self) -> str:
        return f'{self.__class__.__name__}(role={repr(self.role)}, content=...)'


class SystemMessage(BaseMessage):
    """System message for setting context."""
    role: Literal['system'] = 'system'


class UserMessage(BaseMessage):
    """User message for input."""
    role: Literal['user'] = 'user'


class AssistantMessage(BaseMessage):
    """Assistant message for responses."""
    role: Literal['assistant'] = 'assistant'
    tool_calls: Optional[List[ToolCall]] = None


class ToolMessage(BaseMessage):
    """Tool message for function results."""
    role: Literal['tool'] = 'tool'
    tool_call_id: str
    name: str


# Helper functions for creating messages
def create_text_message(role: str, text: str, cache: bool = False) -> BaseMessage:
    """Create a simple text message."""
    message_classes = {
        'system': SystemMessage,
        'user': UserMessage,
        'assistant': AssistantMessage,
        'tool': ToolMessage
    }
    
    message_class = message_classes.get(role, BaseMessage)
    return message_class(role=role, content=text, cache=cache)


def create_image_message(role: str, image_data: bytes, text: str = "", 
                        media_type: SupportedImageMediaType = 'image/png',
                        detail: Literal['auto', 'low', 'high'] = 'auto') -> BaseMessage:
    """Create a message with image content."""
    # Convert image data to base64
    base64_image = base64.b64encode(image_data).decode('utf-8')
    data_url = f"data:{media_type};base64,{base64_image}"
    
    content_parts = []
    if text:
        content_parts.append(ContentPartTextParam(text=text))
    
    content_parts.append(ContentPartImageParam(
        image_url=ImageURL(url=data_url, detail=detail, media_type=media_type)
    ))
    
    message_classes = {
        'system': SystemMessage,
        'user': UserMessage,
        'assistant': AssistantMessage,
        'tool': ToolMessage
    }
    
    message_class = message_classes.get(role, BaseMessage)
    return message_class(role=role, content=content_parts)


def create_tool_call_message(tool_calls: List[ToolCall]) -> AssistantMessage:
    """Create an assistant message with tool calls."""
    return AssistantMessage(
        role='assistant',
        content="",
        tool_calls=tool_calls
    )


def create_tool_result_message(tool_call_id: str, name: str, result: str) -> ToolMessage:
    """Create a tool result message."""
    return ToolMessage(
        role='tool',
        content=result,
        tool_call_id=tool_call_id,
        name=name
    )
