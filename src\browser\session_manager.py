"""
Browser session management and persistence.
"""

import json
import async<PERSON>
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime, timedelta

from ..utils import get_logger, safe_execute_async

logger = get_logger(__name__)


class SessionManager:
    """Manages browser sessions and their persistence."""
    
    def __init__(self, sessions_dir: str = "logs/sessions"):
        self.sessions_dir = Path(sessions_dir)
        self.sessions_dir.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger("session_manager")
        
        # Active sessions tracking
        self.active_sessions = {}
        self.session_metadata = {}
    
    async def create_session(self, session_id: str, context, metadata: Dict[str, Any] = None) -> bool:
        """Create a new browser session."""
        try:
            session_data = {
                "session_id": session_id,
                "created_at": datetime.now().isoformat(),
                "metadata": metadata or {},
                "cookies": [],
                "local_storage": {},
                "session_storage": {},
                "current_url": "",
                "pages": []
            }
            
            # Store session data
            self.active_sessions[session_id] = {
                "context": context,
                "data": session_data,
                "last_accessed": datetime.now()
            }
            
            self.session_metadata[session_id] = metadata or {}
            
            self.logger.info(f"Created session: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create session {session_id}: {e}")
            return False
    
    async def save_session(self, session_id: str, context = None) -> bool:
        """Save a browser session to disk."""
        try:
            if session_id not in self.active_sessions and not context:
                self.logger.error(f"Session {session_id} not found")
                return False
            
            # Get context from active sessions or use provided
            session_context = context or self.active_sessions[session_id]["context"]
            session_data = self.active_sessions.get(session_id, {}).get("data", {})
            
            # Extract session information
            cookies = await session_context.cookies()
            
            # Get all pages in the context
            pages_data = []
            for page in session_context.pages:
                try:
                    page_data = {
                        "url": page.url,
                        "title": await safe_execute_async(page.title, default_return=""),
                        "viewport": page.viewport_size if hasattr(page, 'viewport_size') else None
                    }
                    pages_data.append(page_data)
                except:
                    continue
            
            # Update session data
            session_data.update({
                "session_id": session_id,
                "saved_at": datetime.now().isoformat(),
                "cookies": cookies,
                "pages": pages_data,
                "current_url": pages_data[0]["url"] if pages_data else "",
                "metadata": self.session_metadata.get(session_id, {})
            })
            
            # Save to file
            session_file = self.sessions_dir / f"{session_id}.json"
            with open(session_file, "w") as f:
                json.dump(session_data, f, indent=2, default=str)
            
            # Update active session
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["data"] = session_data
                self.active_sessions[session_id]["last_accessed"] = datetime.now()
            
            self.logger.info(f"Saved session: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save session {session_id}: {e}")
            return False
    
    async def load_session(self, session_id: str, context) -> bool:
        """Load a browser session from disk."""
        try:
            session_file = self.sessions_dir / f"{session_id}.json"
            
            if not session_file.exists():
                self.logger.error(f"Session file not found: {session_file}")
                return False
            
            # Load session data
            with open(session_file, "r") as f:
                session_data = json.load(f)
            
            # Restore cookies
            if "cookies" in session_data and session_data["cookies"]:
                await context.add_cookies(session_data["cookies"])
            
            # Create pages and navigate to saved URLs
            if "pages" in session_data:
                for page_data in session_data["pages"]:
                    try:
                        page = await context.new_page()
                        if page_data.get("url"):
                            await page.goto(page_data["url"], wait_until="domcontentloaded")
                    except Exception as e:
                        self.logger.warning(f"Failed to restore page {page_data.get('url')}: {e}")
            
            # Update active sessions
            self.active_sessions[session_id] = {
                "context": context,
                "data": session_data,
                "last_accessed": datetime.now()
            }
            
            self.session_metadata[session_id] = session_data.get("metadata", {})
            
            self.logger.info(f"Loaded session: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load session {session_id}: {e}")
            return False
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            return {
                "session_id": session_id,
                "created_at": session["data"].get("created_at"),
                "last_accessed": session["last_accessed"].isoformat(),
                "metadata": session["data"].get("metadata", {}),
                "pages_count": len(session["data"].get("pages", [])),
                "current_url": session["data"].get("current_url", "")
            }
        
        # Try to load from file
        session_file = self.sessions_dir / f"{session_id}.json"
        if session_file.exists():
            try:
                with open(session_file, "r") as f:
                    session_data = json.load(f)
                return {
                    "session_id": session_id,
                    "created_at": session_data.get("created_at"),
                    "saved_at": session_data.get("saved_at"),
                    "metadata": session_data.get("metadata", {}),
                    "pages_count": len(session_data.get("pages", [])),
                    "current_url": session_data.get("current_url", "")
                }
            except:
                pass
        
        return None
    
    def list_sessions(self) -> List[Dict[str, Any]]:
        """List all available sessions."""
        sessions = []
        
        # Add active sessions
        for session_id in self.active_sessions:
            info = self.get_session_info(session_id)
            if info:
                info["status"] = "active"
                sessions.append(info)
        
        # Add saved sessions
        for session_file in self.sessions_dir.glob("*.json"):
            session_id = session_file.stem
            if session_id not in self.active_sessions:
                info = self.get_session_info(session_id)
                if info:
                    info["status"] = "saved"
                    sessions.append(info)
        
        return sessions
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session."""
        try:
            # Remove from active sessions
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            if session_id in self.session_metadata:
                del self.session_metadata[session_id]
            
            # Remove session file
            session_file = self.sessions_dir / f"{session_id}.json"
            if session_file.exists():
                session_file.unlink()
            
            self.logger.info(f"Deleted session: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete session {session_id}: {e}")
            return False
    
    async def cleanup_old_sessions(self, max_age_days: int = 30) -> int:
        """Clean up old session files."""
        try:
            cutoff_date = datetime.now() - timedelta(days=max_age_days)
            cleaned_count = 0
            
            for session_file in self.sessions_dir.glob("*.json"):
                try:
                    # Check file modification time
                    file_time = datetime.fromtimestamp(session_file.stat().st_mtime)
                    
                    if file_time < cutoff_date:
                        session_id = session_file.stem
                        
                        # Don't delete active sessions
                        if session_id not in self.active_sessions:
                            session_file.unlink()
                            cleaned_count += 1
                            self.logger.debug(f"Cleaned up old session: {session_id}")
                
                except Exception as e:
                    self.logger.warning(f"Failed to clean up session file {session_file}: {e}")
            
            self.logger.info(f"Cleaned up {cleaned_count} old sessions")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old sessions: {e}")
            return 0
    
    async def export_session(self, session_id: str, export_path: str) -> bool:
        """Export a session to a specific path."""
        try:
            session_info = self.get_session_info(session_id)
            if not session_info:
                return False
            
            session_file = self.sessions_dir / f"{session_id}.json"
            if not session_file.exists():
                return False
            
            # Copy session file to export path
            import shutil
            shutil.copy2(session_file, export_path)
            
            self.logger.info(f"Exported session {session_id} to {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export session {session_id}: {e}")
            return False
    
    async def import_session(self, session_file_path: str, new_session_id: str = None) -> Optional[str]:
        """Import a session from a file."""
        try:
            import_path = Path(session_file_path)
            if not import_path.exists():
                return None
            
            # Load session data
            with open(import_path, "r") as f:
                session_data = json.load(f)
            
            # Generate new session ID if not provided
            session_id = new_session_id or f"imported_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Update session data
            session_data["session_id"] = session_id
            session_data["imported_at"] = datetime.now().isoformat()
            
            # Save to sessions directory
            session_file = self.sessions_dir / f"{session_id}.json"
            with open(session_file, "w") as f:
                json.dump(session_data, f, indent=2, default=str)
            
            self.logger.info(f"Imported session as {session_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Failed to import session from {session_file_path}: {e}")
            return None
