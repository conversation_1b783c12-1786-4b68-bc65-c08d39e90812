"""
Utility modules for the browser automation system.
"""

from .config import config, AppConfig
from .logging import get_logger, AgentLogger, setup_logging
from .error_handling import (
    BrowserAutomationError,
    BrowserConnectionError,
    ElementNotFoundError,
    TaskExecutionError,
    LLMError,
    ConfigurationError,
    TimeoutError,
    RetryableError,
    NetworkError,
    TemporaryBrowserError,
    retry_on_exception,
    timeout_after,
    ErrorHandler,
    safe_execute,
    safe_execute_async,
    get_error_context
)

__all__ = [
    # Configuration
    "config",
    "AppConfig",
    
    # Logging
    "get_logger",
    "AgentLogger",
    "setup_logging",
    
    # Error handling
    "BrowserAutomationError",
    "BrowserConnectionError",
    "ElementNotFoundError",
    "TaskExecutionError",
    "LLMError",
    "ConfigurationError",
    "TimeoutError",
    "RetryableError",
    "NetworkError",
    "TemporaryBrowserError",
    "retry_on_exception",
    "timeout_after",
    "<PERSON>rrorHandler",
    "safe_execute",
    "safe_execute_async",
    "get_error_context"
]
