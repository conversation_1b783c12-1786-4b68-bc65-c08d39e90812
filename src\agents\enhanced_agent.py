"""
Enhanced agent implementation based on browser-use patterns.
Features proper state management, event-driven architecture, and robust error handling.
"""

import asyncio
import gc
import json
import logging
import time
import traceback
import tempfile
from collections.abc import Awaitable, Callable
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Generic, TypeVar
from uuid import uuid4

from pydantic import BaseModel, Field, ConfigDict
from uuid_extensions import uuid7str

from ..utils import get_logger
from ..utils.exceptions import (
    AgentException, BrowserError, TimeoutError, MaxRetriesExceededError,
    handle_browser_error, create_recovery_context
)
from ..utils.logging_config import setup_logging
from ..config.service import CONFIG, load_config
from ..llm import create_llm, BaseLLMProvider
from ..llm.messages import BaseMessage, SystemMessage, UserMessage, AssistantMessage
from ..browser import BrowserManager, SessionManager
from ..tools import BrowserTools
from ..events import default_event_bus
from .message_manager import MessageManager, MessageManagerState

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)


class AgentSettings(BaseModel):
    """Configuration options for the Enhanced Agent."""
    
    use_vision: bool = True
    vision_detail_level: str = 'auto'  # 'auto', 'low', 'high'
    save_conversation_path: Optional[Union[str, Path]] = None
    max_failures: int = 3
    max_actions_per_step: int = 4
    use_thinking: bool = True
    flash_mode: bool = False
    max_history_items: Optional[int] = None
    calculate_cost: bool = False
    llm_timeout: int = 60
    step_timeout: int = 180
    final_response_after_failure: bool = True


class AgentState(BaseModel):
    """Holds all state information for an Enhanced Agent."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    agent_id: str = Field(default_factory=uuid7str)
    n_steps: int = 1
    consecutive_failures: int = 0
    last_result: Optional[List[Dict[str, Any]]] = None
    last_plan: Optional[str] = None
    last_model_output: Optional[Dict[str, Any]] = None

    # Pause/resume state
    paused: bool = False
    stopped: bool = False
    session_initialized: bool = False
    follow_up_task: bool = False

    # Execution context
    current_task: Optional[str] = None
    task_history: List[Dict[str, Any]] = Field(default_factory=list)
    execution_context: Dict[str, Any] = Field(default_factory=dict)

    # Message manager state
    message_manager_state: MessageManagerState = Field(default_factory=MessageManagerState)

    # Performance tracking
    stats: Dict[str, Any] = Field(default_factory=lambda: {
        "tasks_completed": 0,
        "tasks_failed": 0,
        "total_execution_time": 0.0,
        "created_at": datetime.now().isoformat()
    })


class AgentStepInfo:
    """Information about the current step."""
    
    def __init__(self, step_number: int, max_steps: int):
        self.step_number = step_number
        self.max_steps = max_steps
    
    def is_last_step(self) -> bool:
        """Check if this is the last step."""
        return self.step_number >= self.max_steps - 1


class ActionResult(BaseModel):
    """Result of executing an action."""
    
    # For done action
    is_done: bool = False
    success: Optional[bool] = None
    
    # Error handling
    error: Optional[str] = None
    
    # Files and attachments
    attachments: Optional[List[str]] = None
    
    # Always include in long term memory
    extracted_content: Optional[str] = None
    include_extracted_content_only_once: bool = False
    
    # Additional metadata
    action_type: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class AgentOutput(BaseModel):
    """Output from the agent's LLM processing."""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    # Current state information
    current_state: 'AgentCurrentState' = Field(default_factory=lambda: AgentCurrentState())

    # Action to execute
    action: Optional[Dict[str, Any]] = None

    # Structured output if requested
    structured_output: Optional[BaseModel] = None

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentCurrentState(BaseModel):
    """Current state of the agent during processing."""

    # Thinking process (if enabled)
    thinking: Optional[str] = None

    # Evaluation of previous goal
    evaluation_previous_goal: Optional[str] = None

    # Memory/summary of current situation
    memory: Optional[str] = None

    # Next goal/plan
    next_goal: Optional[str] = None

    # Current step information
    step_info: Optional[str] = None


class StepMetadata(BaseModel):
    """Metadata for a single step execution."""

    step_number: int
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = False
    error: Optional[str] = None
    actions_executed: List[str] = Field(default_factory=list)
    tokens_used: Optional[int] = None
    cost: Optional[float] = None


class EnhancedAgent:
    """Enhanced agent with production-ready patterns from browser-use."""
    
    def __init__(
        self,
        task: str = None,
        llm: Optional[BaseLLMProvider] = None,
        browser_session: Optional[BrowserManager] = None,
        settings: Optional[AgentSettings] = None,
        agent_id: Optional[str] = None,
        **kwargs
    ):
        # Initialize core components
        self.state = AgentState(agent_id=agent_id or uuid7str())
        self.settings = settings or AgentSettings()
        self.logger = get_logger(f"Agent.{self.state.agent_id[-4:]}")

        # Task and LLM setup
        self.task = task
        self.llm = llm or self._create_default_llm()

        # Browser management
        self.browser_session = browser_session or BrowserManager()
        self.session_manager = SessionManager()
        
        # Tools
        self.tools: Optional[BrowserTools] = None

        # Message manager for conversation handling
        self.message_manager = MessageManager(
            max_history_items=self.settings.max_history_items,
            vision_detail_level=self.settings.vision_detail_level
        )

        # Event callbacks
        self.task_callbacks: Dict[str, List[Callable]] = {}
        self.error_callbacks: List[Callable] = []

        # Initialization flag
        self.is_initialized = False

        # Performance tracking
        self.step_metadata: List[StepMetadata] = []
        self.total_execution_time = 0.0

        # Recovery and error handling
        self.consecutive_failures = 0
        self.recovery_attempts: List[str] = []

        # Event system
        self.event_bus = default_event_bus

        # Update state with initial task
        if task:
            self.state.current_task = task

        # Initialize logging
        self.logger.info(f"Enhanced Agent initialized with ID: {self.state.agent_id}")

        # Setup event handlers
        self._setup_event_handlers()
    
    def _create_default_llm(self) -> BaseLLMProvider:
        """Create default LLM from configuration."""
        config = load_config()
        llm_config = config.get('llm', {})

        provider = llm_config.get('provider', 'gemini')
        model = llm_config.get('model', 'gemini-2.0-flash-exp')
        api_key = llm_config.get('api_key', '')

        return create_llm(provider=provider, model=model, api_key=api_key)

    def _setup_event_handlers(self):
        """Setup event handlers for the agent."""
        # Browser events
        self.event_bus.subscribe('navigation_complete', self._on_navigation_complete)
        self.event_bus.subscribe('page_load_error', self._on_page_load_error)
        self.event_bus.subscribe('browser_error', self._on_browser_error)

        # Agent events
        self.event_bus.subscribe('step_started', self._on_step_started)
        self.event_bus.subscribe('step_completed', self._on_step_completed)
        self.event_bus.subscribe('step_failed', self._on_step_failed)

    async def _on_navigation_complete(self, event_data: Dict[str, Any]):
        """Handle navigation complete event."""
        self.logger.debug(f"Navigation completed: {event_data.get('url', 'unknown')}")

    async def _on_page_load_error(self, event_data: Dict[str, Any]):
        """Handle page load error event."""
        self.logger.warning(f"Page load error: {event_data.get('error', 'unknown')}")
        self.consecutive_failures += 1

    async def _on_browser_error(self, event_data: Dict[str, Any]):
        """Handle browser error event."""
        self.logger.error(f"Browser error: {event_data.get('error', 'unknown')}")
        self.consecutive_failures += 1

    async def _on_step_started(self, event_data: Dict[str, Any]):
        """Handle step started event."""
        step_number = event_data.get('step_number', 0)
        self.logger.info(f"Step {step_number} started")

    async def _on_step_completed(self, event_data: Dict[str, Any]):
        """Handle step completed event."""
        step_number = event_data.get('step_number', 0)
        self.logger.info(f"Step {step_number} completed successfully")
        self.consecutive_failures = 0  # Reset failure count on success

    async def _on_step_failed(self, event_data: Dict[str, Any]):
        """Handle step failed event."""
        step_number = event_data.get('step_number', 0)
        error = event_data.get('error', 'unknown')
        self.logger.error(f"Step {step_number} failed: {error}")
        self.consecutive_failures += 1

    async def _setup_system_message(self):
        """Set up the system message for the agent."""
        system_prompt = f"""You are an AI agent designed to help with browser automation tasks.

Your current task is: {self.state.current_task or 'No specific task assigned'}

You have access to browser automation tools that allow you to:
- Navigate to web pages
- Click on elements
- Type text into forms
- Take screenshots
- Extract information from pages

Always think step by step and explain your reasoning.
When you complete the task successfully, use the 'done' action.
If you encounter errors, try alternative approaches.

Remember to be precise with element selection and wait for pages to load completely."""

        system_message = SystemMessage(content=system_prompt, cache=True)
        self.message_manager.set_system_message(system_message)

        logger.debug("System message configured")

    async def initialize(self) -> bool:
        """Initialize the enhanced agent."""
        try:
            self.logger.info("Initializing enhanced agent")
            
            # Initialize browser session
            if not self.browser_session.is_initialized:
                await self.browser_session.initialize()
            
            # Initialize tools
            self.tools = BrowserTools(self.browser_session, self.logger)
            await self.tools.initialize()
            
            # Create default session
            await self.session_manager.create_session(
                session_id="default",
                context=self.browser_session.context,
                metadata={"agent_id": self.state.agent_id}
            )
            
            # Set up system message
            await self._setup_system_message()

            self.is_initialized = True
            self.state.session_initialized = True

            self.logger.info("Enhanced agent initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize enhanced agent: {e}")
            self.error_handler.handle_task_error(e, "agent_initialization")
            return False
    
    async def run(self, task: str = None, max_steps: int = 10) -> Dict[str, Any]:
        """Main entry point for running tasks with enhanced error handling."""
        if not self.is_initialized:
            await self.initialize()
        
        # Use provided task or stored task
        current_task = task or self.task
        if not current_task:
            raise ValueError("No task provided")
        
        self.state.current_task = current_task
        task_id = str(uuid4())
        start_time = datetime.now()
        
        try:
            self.logger.log_task_start(current_task, task_id)
            
            # Execute the task with step-by-step processing
            result = await self._execute_task_with_steps(current_task, max_steps)
            
            # Calculate execution time
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Update state
            task_record = {
                "id": task_id,
                "description": current_task,
                "started_at": start_time.isoformat(),
                "completed_at": end_time.isoformat(),
                "execution_time": execution_time,
                "result": result,
                "success": result.get("success", False)
            }
            
            self.state.task_history.append(task_record)
            self.state.stats["tasks_completed"] += 1
            self.state.stats["total_execution_time"] += execution_time
            
            # Log completion
            self.logger.log_task_complete(current_task, task_id, execution_time)
            
            # Call completion callbacks
            await self._call_task_callbacks("completed", task_record)
            
            return result
            
        except Exception as e:
            # Handle error
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            error_info = self.error_handler.handle_task_error(e, current_task)
            
            # Update state
            task_record = {
                "id": task_id,
                "description": current_task,
                "started_at": start_time.isoformat(),
                "failed_at": end_time.isoformat(),
                "execution_time": execution_time,
                "error": str(e),
                "error_info": error_info,
                "success": False
            }
            
            self.state.task_history.append(task_record)
            self.state.stats["tasks_failed"] += 1
            self.state.stats["total_execution_time"] += execution_time
            self.state.consecutive_failures += 1
            
            # Take screenshot for debugging
            screenshot_data = await self._take_error_screenshot()
            if screenshot_data:
                self.logger.log_error_with_screenshot(e, screenshot_data)
            
            # Call error callbacks
            await self._call_error_callbacks(e, task_record)
            
            # Check if we should attempt final recovery
            if (self.settings.final_response_after_failure and 
                self.state.consecutive_failures >= self.settings.max_failures):
                
                try:
                    recovery_result = await self._attempt_final_recovery(current_task, str(e))
                    if recovery_result.get("success"):
                        return recovery_result
                except Exception as recovery_error:
                    self.logger.error(f"Final recovery attempt failed: {recovery_error}")
            
            # Re-raise if not recoverable
            if not error_info.get("recoverable", False):
                raise TaskExecutionError(f"Task failed: {e}") from e
            
            return {
                "success": False,
                "error": str(e),
                "error_info": error_info,
                "task_id": task_id
            }
    
    async def _execute_task_with_steps(self, task: str, max_steps: int) -> Dict[str, Any]:
        """Execute task with step-by-step processing."""
        self.logger.info(f"Executing task with max {max_steps} steps: {task}")
        
        step_results = []
        
        for step in range(max_steps):
            step_info = AgentStepInfo(step, max_steps)
            
            try:
                self.logger.info(f"Step {step + 1}/{max_steps}")
                
                # Execute single step
                step_result = await self._execute_single_step(task, step_info)
                step_results.append(step_result)
                
                # Check if task is complete
                if step_result.get("is_done", False):
                    self.logger.info(f"Task completed in {step + 1} steps")
                    return {
                        "success": True,
                        "steps": step_results,
                        "total_steps": step + 1,
                        "final_result": step_result
                    }
                
                # Reset consecutive failures on successful step
                self.state.consecutive_failures = 0
                
            except Exception as e:
                self.logger.error(f"Step {step + 1} failed: {e}")
                self.state.consecutive_failures += 1
                
                step_results.append({
                    "step": step + 1,
                    "error": str(e),
                    "success": False
                })
                
                # Check if we should stop due to too many failures
                if self.state.consecutive_failures >= self.settings.max_failures:
                    self.logger.error(f"Stopping due to {self.settings.max_failures} consecutive failures")
                    break
        
        # Task didn't complete within max steps
        return {
            "success": False,
            "steps": step_results,
            "total_steps": len(step_results),
            "error": f"Task did not complete within {max_steps} steps"
        }
    
    async def _execute_single_step(self, task: str, step_info: AgentStepInfo) -> Dict[str, Any]:
        """Execute a single step of the task."""
        # Get current page state
        page_info = await self._get_current_page_info()
        
        # Prepare context for LLM
        context = {
            "task": task,
            "step_number": step_info.step_number + 1,
            "max_steps": step_info.max_steps,
            "is_last_step": step_info.is_last_step(),
            "page_info": page_info,
            "available_tools": self.tools.get_available_tools() if self.tools else [],
            "execution_context": self.state.execution_context
        }
        
        # Generate action plan using LLM
        action_plan = await self._generate_action_plan(context)
        
        # Execute the planned actions
        execution_result = await self._execute_action_plan(action_plan)
        
        # Update execution context
        self.state.execution_context.update({
            "last_action": action_plan.get("action"),
            "last_result": execution_result,
            "step_number": step_info.step_number + 1
        })
        
        return execution_result
    
    async def _generate_action_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate action plan using LLM."""
        # This would use the LLM to generate a structured action plan
        # For now, return a simple plan structure
        return {
            "action": "navigate",
            "parameters": {"url": "https://example.com"},
            "reasoning": "Starting with navigation to example site"
        }
    
    async def _execute_action_plan(self, action_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the generated action plan."""
        action = action_plan.get("action")
        parameters = action_plan.get("parameters", {})
        
        try:
            if action == "navigate" and self.tools:
                success = await self.tools.navigate_to(parameters.get("url"))
                return {
                    "success": success,
                    "action": action,
                    "parameters": parameters,
                    "is_done": False
                }
            
            # Add more action handlers here
            return {
                "success": False,
                "error": f"Unknown action: {action}",
                "action": action,
                "parameters": parameters
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": action,
                "parameters": parameters
            }
    
    async def _get_current_page_info(self) -> Dict[str, Any]:
        """Get information about the current page."""
        try:
            if self.browser_session and self.browser_session.is_initialized:
                page = await self.browser_session.get_page()
                return {
                    "url": await self.browser_session.get_current_url(page),
                    "title": await self.browser_session.get_page_title(page),
                    "content_length": len(await self.browser_session.get_page_content(page))
                }
        except Exception as e:
            self.logger.warning(f"Failed to get page info: {e}")
        
        return {"url": "", "title": "", "content_length": 0}
    
    async def _take_error_screenshot(self) -> Optional[bytes]:
        """Take a screenshot for error debugging."""
        try:
            if self.browser_session and self.browser_session.is_initialized:
                page = await self.browser_session.get_page()
                screenshot_path = await self.browser_session.take_screenshot(page)
                if screenshot_path:
                    with open(screenshot_path, "rb") as f:
                        return f.read()
        except Exception as e:
            self.logger.warning(f"Failed to take error screenshot: {e}")
        return None
    
    async def _attempt_final_recovery(self, task: str, error: str) -> Dict[str, Any]:
        """Attempt final recovery after max failures."""
        self.logger.info("Attempting final recovery")
        
        try:
            # Simple recovery: take screenshot and return current state
            screenshot_path = None
            if self.browser_session and self.browser_session.is_initialized:
                page = await self.browser_session.get_page()
                screenshot_path = await self.browser_session.take_screenshot(page)
            
            return {
                "success": True,
                "recovery_attempt": True,
                "final_state": await self._get_current_page_info(),
                "screenshot": screenshot_path,
                "original_error": error
            }
            
        except Exception as e:
            return {
                "success": False,
                "recovery_attempt": True,
                "recovery_error": str(e),
                "original_error": error
            }
    
    async def _call_task_callbacks(self, event: str, task_data: Dict[str, Any]):
        """Call registered task callbacks."""
        callbacks = self.task_callbacks.get(event, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task_data)
                else:
                    callback(task_data)
            except Exception as e:
                self.logger.warning(f"Task callback failed: {e}")
    
    async def _call_error_callbacks(self, error: Exception, task_data: Dict[str, Any]):
        """Call registered error callbacks."""
        for callback in self.error_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(error, task_data)
                else:
                    callback(error, task_data)
            except Exception as e:
                self.logger.warning(f"Error callback failed: {e}")
    
    def register_task_callback(self, event: str, callback: Callable):
        """Register a callback for task events."""
        if event not in self.task_callbacks:
            self.task_callbacks[event] = []
        self.task_callbacks[event].append(callback)
    
    def register_error_callback(self, callback: Callable):
        """Register a callback for errors."""
        self.error_callbacks.append(callback)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get agent performance statistics."""
        stats = self.state.stats.copy()
        stats.update({
            "agent_id": self.state.agent_id,
            "is_initialized": self.is_initialized,
            "current_task": self.state.current_task is not None,
            "total_tasks": len(self.state.task_history),
            "success_rate": (
                self.state.stats["tasks_completed"] / 
                (self.state.stats["tasks_completed"] + self.state.stats["tasks_failed"])
                if (self.state.stats["tasks_completed"] + self.state.stats["tasks_failed"]) > 0
                else 0
            )
        })
        return stats
    
    def get_task_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get task execution history."""
        history = self.state.task_history.copy()
        if limit:
            history = history[-limit:]
        return history

    async def _call_llm_with_message_manager(
        self,
        user_message: str,
        images: Optional[List[bytes]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Call LLM using the message manager for conversation handling."""
        try:
            # Create user message
            user_msg = self.message_manager.create_user_message(
                text=user_message,
                images=images,
                metadata=metadata
            )

            # Add to conversation history
            self.message_manager.add_message(user_msg)

            # Get messages for LLM
            messages = self.message_manager.get_messages_for_llm()

            # Call LLM
            response = await self.llm.generate_response(messages)

            # Create assistant message
            assistant_msg = self.message_manager.create_assistant_message(
                text=response.content,
                metadata={"tokens": response.usage.total_tokens if response.usage else None}
            )

            # Add to conversation history
            self.message_manager.add_message(
                assistant_msg,
                tokens=response.usage.total_tokens if response.usage else None,
                cost=response.usage.total_cost if hasattr(response.usage, 'total_cost') else None
            )

            return response.content

        except Exception as e:
            self.logger.error(f"LLM call failed: {e}")
            raise

    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get conversation summary from message manager."""
        return self.message_manager.get_conversation_summary()

    def export_conversation(self) -> List[Dict[str, Any]]:
        """Export conversation history."""
        return self.message_manager.export_conversation()

    async def _attempt_recovery(self) -> Dict[str, Any]:
        """Attempt to recover from consecutive failures."""
        self.logger.info("Attempting recovery from consecutive failures")

        recovery_strategies = [
            "refresh_page",
            "clear_browser_cache",
            "restart_browser_session",
            "reset_agent_state"
        ]

        for strategy in recovery_strategies:
            try:
                self.logger.info(f"Trying recovery strategy: {strategy}")
                self.recovery_attempts.append(strategy)

                if strategy == "refresh_page":
                    if hasattr(self.browser_session, 'refresh_page'):
                        await self.browser_session.refresh_page()
                elif strategy == "clear_browser_cache":
                    if hasattr(self.browser_session, 'clear_cache'):
                        await self.browser_session.clear_cache()
                elif strategy == "restart_browser_session":
                    if hasattr(self.browser_session, 'restart'):
                        await self.browser_session.restart()
                elif strategy == "reset_agent_state":
                    self.consecutive_failures = 0
                    self.state.consecutive_failures = 0

                # Test if recovery was successful
                test_result = await self._test_recovery()
                if test_result:
                    self.logger.info(f"Recovery successful using strategy: {strategy}")
                    self.consecutive_failures = 0
                    return {
                        'success': True,
                        'strategy': strategy,
                        'message': f'Recovered using {strategy}'
                    }

            except Exception as e:
                self.logger.warning(f"Recovery strategy '{strategy}' failed: {e}")
                continue

        # All recovery strategies failed
        self.logger.error("All recovery strategies failed")
        return {
            'success': False,
            'strategies_attempted': recovery_strategies,
            'message': 'All recovery strategies failed'
        }

    async def _test_recovery(self) -> bool:
        """Test if recovery was successful by performing a simple operation."""
        try:
            # Simple test: check if browser session is responsive
            if self.browser_session and hasattr(self.browser_session, 'get_current_url'):
                url = await self.browser_session.get_current_url()
                return url is not None
            return True
        except Exception:
            return False

    async def _attempt_final_recovery(self, task: str, error: str) -> Dict[str, Any]:
        """Attempt final recovery after max failures reached."""
        self.logger.info("Attempting final recovery after max failures")

        try:
            # Create recovery context
            recovery_context = create_recovery_context(
                error=Exception(error),
                recovery_attempts=self.recovery_attempts,
                additional_context={
                    'task': task,
                    'consecutive_failures': self.consecutive_failures,
                    'agent_id': self.state.agent_id
                }
            )

            # Try one final LLM call with recovery context
            recovery_prompt = f"""
            The agent has encountered {self.consecutive_failures} consecutive failures while trying to complete the task: {task}

            Last error: {error}

            Recovery attempts made: {', '.join(self.recovery_attempts)}

            Please provide a final assessment and any remaining actions that could be taken to complete or partially complete the task.
            """

            response = await self._call_llm_with_timeout(recovery_prompt)

            return {
                'success': True,
                'response': response,
                'recovery_context': recovery_context,
                'message': 'Final recovery attempt completed'
            }

        except Exception as e:
            self.logger.error(f"Final recovery attempt failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Final recovery attempt failed'
            }

    async def _call_llm_with_timeout(self, prompt: str, timeout: Optional[int] = None) -> str:
        """Call LLM with timeout handling."""
        timeout = timeout or self.settings.llm_timeout

        try:
            response = await asyncio.wait_for(
                self._call_llm(prompt),
                timeout=timeout
            )
            return response
        except asyncio.TimeoutError:
            raise TimeoutError(
                operation="LLM call",
                timeout=timeout,
                message=f"LLM call timed out after {timeout}s"
            )

    async def cleanup(self):
        """Clean up agent resources."""
        try:
            self.logger.info("Cleaning up enhanced agent resources")

            # Save current session
            if self.session_manager:
                await self.session_manager.save_session("default", self.browser_session.context)

            # Cleanup browser
            if self.browser_session:
                await self.browser_session.cleanup()

            # Close LLM connection
            if self.llm and hasattr(self.llm, 'close'):
                await self.llm.close()

            self.is_initialized = False
            self.logger.info("Enhanced agent cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
