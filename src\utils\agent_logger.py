"""
Enhanced logging utilities for agents.
"""

import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import structlog


class AgentLogger:
    """Enhanced logger for agent operations."""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.logger = structlog.get_logger("agent").bind(agent_id=agent_id)
        
        # Create agent-specific log file
        log_dir = Path("logs/agents")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        self.log_file = log_dir / f"agent_{agent_id}.log"
        
        # Set up file handler
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # Get the root logger and add our handler
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def log_task_start(self, task: str, task_id: str):
        """Log task start."""
        self.logger.info(
            "Task started",
            task=task,
            task_id=task_id,
            timestamp=datetime.now().isoformat()
        )
    
    def log_task_complete(self, task: str, task_id: str, execution_time: float):
        """Log task completion."""
        self.logger.info(
            "Task completed",
            task=task,
            task_id=task_id,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat()
        )
    
    def log_error_with_screenshot(self, error: Exception, screenshot_data: bytes):
        """Log error with screenshot."""
        screenshot_path = Path("logs/screenshots") / f"error_{self.agent_id}_{int(time.time())}.png"
        screenshot_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(screenshot_path, "wb") as f:
            f.write(screenshot_data)
        
        self.logger.error(
            "Error with screenshot",
            error=str(error),
            screenshot_path=str(screenshot_path),
            timestamp=datetime.now().isoformat()
        )


class ErrorHandler:
    """Enhanced error handler for agents."""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.logger = AgentLogger(agent_id)
    
    def handle_task_error(self, error: Exception, context: str) -> Dict[str, Any]:
        """Handle task execution error."""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context,
            "recoverable": self._is_recoverable_error(error),
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.error(
            f"Task error in {context}",
            error_type=error_info["error_type"],
            error_message=error_info["error_message"],
            recoverable=error_info["recoverable"]
        )
        
        return error_info
    
    def _is_recoverable_error(self, error: Exception) -> bool:
        """Determine if an error is recoverable."""
        recoverable_errors = [
            "TimeoutError",
            "NetworkError",
            "ElementNotFoundError",
            "NavigationError"
        ]
        
        return type(error).__name__ in recoverable_errors


class TaskExecutionError(Exception):
    """Custom exception for task execution failures."""
    pass
