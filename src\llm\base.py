"""
Enhanced base classes for LLM providers with browser-use patterns.
Features proper message handling, structured outputs, and cost tracking.
"""

import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union, AsyncGenerator, TypeVar, Generic, Protocol, overload, runtime_checkable
from uuid import uuid4
from datetime import datetime

from pydantic import BaseModel, Field

from ..utils import get_logger
from .messages import BaseMessage
from .views import ChatInvokeCompletion, ChatInvokeUsage, LLMResponse, ModelCapabilities, ProviderConfig, TokenCostTracker

logger = get_logger(__name__)

T = TypeVar('T', bound=BaseModel)


@runtime_checkable
class BaseChatModel(Protocol):
    """
    Protocol for chat models based on browser-use patterns.
    Defines the interface that all LLM providers must implement.
    """
    
    _verified_api_keys: bool = False
    model: str
    
    @property
    def provider(self) -> str:
        """Get the provider name."""
        ...
    
    @property
    def name(self) -> str:
        """Get the model name."""
        ...
    
    @property
    def model_name(self) -> str:
        """Get the model name (legacy support)."""
        return self.model
    
    @overload
    async def ainvoke(self, messages: List[BaseMessage], output_format: None = None) -> ChatInvokeCompletion[str]:
        ...
    
    @overload
    async def ainvoke(self, messages: List[BaseMessage], output_format: type[T]) -> ChatInvokeCompletion[T]:
        ...
    
    async def ainvoke(
        self, 
        messages: List[BaseMessage], 
        output_format: Optional[type[T]] = None
    ) -> Union[ChatInvokeCompletion[T], ChatInvokeCompletion[str]]:
        """Invoke the model with messages and optional structured output format."""
        ...
    
    @classmethod
    def __get_pydantic_core_schema__(cls, source_type: type, handler: Any) -> Any:
        """Allow this Protocol to be used in Pydantic models."""
        from pydantic_core import core_schema
        return core_schema.any_schema()


class BaseLLMProvider(ABC):
    """
    Enhanced base class for LLM providers with browser-use patterns.
    
    Features:
    - Proper message handling
    - Structured output support
    - Cost tracking
    - Error handling and retries
    - Performance monitoring
    """
    
    def __init__(self, config: ProviderConfig):
        self.config = config
        self.logger = get_logger(f"{self.__class__.__name__}")
        self.cost_tracker = TokenCostTracker()
        self._capabilities: Optional[ModelCapabilities] = None
        self._initialized = False
    
    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Get the provider name."""
        pass
    
    @property
    def model_name(self) -> str:
        """Get the model name."""
        return self.config.model_name
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the provider."""
        pass
    
    @abstractmethod
    async def get_capabilities(self) -> ModelCapabilities:
        """Get model capabilities."""
        pass
    
    @abstractmethod
    async def _generate_response(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> ChatInvokeCompletion[str]:
        """Generate a response from the model. Override in subclasses."""
        pass
    
    @abstractmethod
    async def _generate_structured_response(
        self,
        messages: List[BaseMessage],
        output_format: type[T],
        **kwargs
    ) -> ChatInvokeCompletion[T]:
        """Generate a structured response from the model. Override in subclasses."""
        pass
    
    async def generate_response(
        self,
        messages: List[BaseMessage],
        output_format: Optional[type[T]] = None,
        **kwargs
    ) -> LLMResponse:
        """
        Generate a response with comprehensive error handling and cost tracking.
        
        Args:
            messages: List of messages to send to the model
            output_format: Optional structured output format
            **kwargs: Additional parameters for the model
            
        Returns:
            LLMResponse with content, usage, and metadata
        """
        if not self._initialized:
            await self.initialize()
        
        request_id = str(uuid4())
        start_time = time.time()
        
        try:
            self.logger.debug(f"Generating response with {self.provider_name}/{self.model_name}")
            
            # Generate response based on output format
            if output_format:
                completion = await self._generate_structured_response(
                    messages, output_format, **kwargs
                )
            else:
                completion = await self._generate_response(messages, **kwargs)
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Update completion metadata
            completion.request_id = request_id
            completion.response_time = response_time
            completion.timestamp = datetime.now().isoformat()
            completion.model_name = self.model_name
            completion.provider = self.provider_name
            
            # Track costs if usage is available
            if completion.usage:
                completion.usage.model_name = self.model_name
                completion.usage.provider = self.provider_name
                self.cost_tracker.add_usage(completion.usage, self.model_name)
            
            # Create unified response
            response = LLMResponse.from_completion(completion, self.provider_name, self.model_name)
            
            self.logger.debug(
                f"Response generated successfully in {response_time:.2f}s "
                f"(tokens: {completion.usage.total_tokens if completion.usage else 'unknown'})"
            )
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            self.logger.error(f"Failed to generate response: {e} (took {response_time:.2f}s)")
            raise
    
    async def generate_streaming_response(
        self,
        messages: List[BaseMessage],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response.
        Override in subclasses that support streaming.
        """
        # Default implementation: fall back to non-streaming
        response = await self.generate_response(messages, **kwargs)
        yield response.content
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get cost tracking summary."""
        return {
            "provider": self.provider_name,
            "model": self.model_name,
            "total_requests": self.cost_tracker.total_requests,
            "total_tokens": self.cost_tracker.total_prompt_tokens + self.cost_tracker.total_completion_tokens,
            "total_cost": self.cost_tracker.total_cost,
            "average_cost_per_request": self.cost_tracker.get_average_cost_per_request(),
            "model_usage": self.cost_tracker.model_usage
        }
    
    def reset_cost_tracking(self):
        """Reset cost tracking statistics."""
        self.cost_tracker.reset()
    
    async def validate_api_key(self) -> bool:
        """Validate the API key by making a test request."""
        try:
            test_messages = [BaseMessage(role="user", content="Hello")]
            await self.generate_response(test_messages)
            return True
        except Exception as e:
            self.logger.error(f"API key validation failed: {e}")
            return False
    
    def __str__(self) -> str:
        return f"{self.provider_name}/{self.model_name}"
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(provider={self.provider_name}, model={self.model_name})"


class LLMProviderError(Exception):
    """Base exception for LLM provider errors."""
    
    def __init__(self, message: str, provider: str, model: str, retryable: bool = False):
        super().__init__(message)
        self.provider = provider
        self.model = model
        self.retryable = retryable


class RateLimitError(LLMProviderError):
    """Rate limit exceeded error."""
    
    def __init__(self, message: str, provider: str, model: str, retry_after: Optional[float] = None):
        super().__init__(message, provider, model, retryable=True)
        self.retry_after = retry_after


class AuthenticationError(LLMProviderError):
    """Authentication error."""
    
    def __init__(self, message: str, provider: str, model: str):
        super().__init__(message, provider, model, retryable=False)


class ModelNotFoundError(LLMProviderError):
    """Model not found error."""
    
    def __init__(self, message: str, provider: str, model: str):
        super().__init__(message, provider, model, retryable=False)


class QuotaExceededError(LLMProviderError):
    """Quota exceeded error."""
    
    def __init__(self, message: str, provider: str, model: str):
        super().__init__(message, provider, model, retryable=False)
