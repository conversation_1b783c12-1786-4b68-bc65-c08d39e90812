"""
Setup script for the browser automation system.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="browser-automation-system",
    version="1.0.0",
    description="Comprehensive browser automation system based on browser-use library",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Browser Automation Team",
    author_email="<EMAIL>",
    url="https://github.com/your-org/browser-automation-system",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "docs": [
            "sphinx>=6.0.0",
            "sphinx-rtd-theme>=1.2.0",
            "myst-parser>=1.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "browser-automation=main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Software Development :: Testing",
    ],
    keywords="browser automation selenium playwright ai llm web scraping testing",
    project_urls={
        "Bug Reports": "https://github.com/your-org/browser-automation-system/issues",
        "Source": "https://github.com/your-org/browser-automation-system",
        "Documentation": "https://browser-automation-system.readthedocs.io/",
    },
    include_package_data=True,
    zip_safe=False,
)
