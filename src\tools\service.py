"""
Enhanced tools service based on browser-use patterns.
Provides comprehensive action management with built-in browser actions.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Callable, Union, TypeVar, Generic
from urllib.parse import quote_plus

from pydantic import BaseModel, Field
from playwright.async_api import Page

from ..utils import get_logger
from ..browser.browser_manager import BrowserManager
from ..dom.service import DOMService
from ..llm.base import BaseLLMProvider
from ..events import default_event_bus
from ..events.browser_events import NavigateToUrlEvent, ClickElementEvent, TypeTextEvent
from .registry import Registry, ActionResult

logger = get_logger(__name__)

T = TypeVar('T', bound=BaseModel)


# Action parameter models
class NavigateAction(BaseModel):
    """Parameters for navigation action."""
    url: str = Field(..., description="URL to navigate to")


class ClickAction(BaseModel):
    """Parameters for click action."""
    selector: Optional[str] = Field(None, description="CSS selector to click")
    index: Optional[int] = Field(None, description="Interactive element index to click")
    x: Optional[float] = Field(None, description="X coordinate to click")
    y: Optional[float] = Field(None, description="Y coordinate to click")


class TypeAction(BaseModel):
    """Parameters for typing action."""
    text: str = Field(..., description="Text to type")
    selector: Optional[str] = Field(None, description="CSS selector of input element")
    index: Optional[int] = Field(None, description="Interactive element index to type in")
    clear_first: bool = Field(True, description="Clear existing text before typing")


class ScrollAction(BaseModel):
    """Parameters for scroll action."""
    direction: str = Field("down", description="Scroll direction: up, down, left, right")
    amount: int = Field(3, description="Number of scroll steps")


class WaitAction(BaseModel):
    """Parameters for wait action."""
    selector: Optional[str] = Field(None, description="CSS selector to wait for")
    timeout: float = Field(5.0, description="Maximum time to wait in seconds")
    visible: bool = Field(True, description="Wait for element to be visible")


class SearchGoogleAction(BaseModel):
    """Parameters for Google search action."""
    query: str = Field(..., description="Search query")


class ExtractTextAction(BaseModel):
    """Parameters for text extraction action."""
    selector: Optional[str] = Field(None, description="CSS selector to extract text from")
    all_text: bool = Field(False, description="Extract all text from page")


class DoneAction(BaseModel):
    """Parameters for task completion action."""
    success: bool = Field(True, description="Whether the task was completed successfully")
    result: Optional[str] = Field(None, description="Task result or summary")


class Tools(Generic[T]):
    """
    Enhanced tools service based on browser-use patterns.
    
    Features:
    - Built-in browser actions
    - Custom action registration
    - Dependency injection
    - Error handling and retries
    - Domain-based filtering
    """
    
    def __init__(
        self,
        exclude_actions: List[str] = None,
        output_model: Optional[type[T]] = None,
        browser_manager: Optional[BrowserManager] = None,
        dom_service: Optional[DOMService] = None,
        llm_provider: Optional[BaseLLMProvider] = None
    ):
        self.registry = Registry(exclude_actions or [])
        self.browser_manager = browser_manager
        self.dom_service = dom_service
        self.llm_provider = llm_provider
        self.output_model = output_model
        
        self.logger = get_logger(f"{self.__class__.__name__}")
        
        # Register built-in actions
        self._register_built_in_actions()
        
        # Register done action with output model
        if output_model:
            self._register_done_action(output_model)
    
    def _register_built_in_actions(self):
        """Register built-in browser actions."""
        
        # Navigation Actions
        @self.registry.action(
            "Navigate to a specific URL",
            param_model=NavigateAction,
            category="navigation",
            requires_browser=True
        )
        async def navigate(params: NavigateAction, browser_manager: BrowserManager):
            try:
                await browser_manager.navigate_to(params.url)
                
                # Emit navigation event
                event = NavigateToUrlEvent(url=params.url)
                await default_event_bus.emit(event)
                
                return ActionResult(
                    success=True,
                    action_type="navigate",
                    extracted_content=f"Navigated to {params.url}",
                    execution_time=0.0
                )
                
            except Exception as e:
                return ActionResult(
                    success=False,
                    action_type="navigate",
                    error=str(e),
                    execution_time=0.0
                )
        
        # Click Actions
        @self.registry.action(
            "Click on an element using selector, index, or coordinates",
            param_model=ClickAction,
            category="interaction",
            requires_browser=True,
            requires_dom=True
        )
        async def click(params: ClickAction, browser_manager: BrowserManager, dom_service: DOMService):
            try:
                page = browser_manager.get_current_page()
                if not page:
                    raise ValueError("No active page available")
                
                clicked = False
                
                # Click by selector
                if params.selector:
                    await page.click(params.selector)
                    clicked = True
                
                # Click by interactive index
                elif params.index is not None:
                    element = await dom_service.get_element_by_index(params.index)
                    if element:
                        clicked = await dom_service.click_element(element)
                    else:
                        raise ValueError(f"No element found with index {params.index}")
                
                # Click by coordinates
                elif params.x is not None and params.y is not None:
                    await page.click(params.x, params.y)
                    clicked = True
                
                else:
                    raise ValueError("Must provide selector, index, or coordinates")
                
                if clicked:
                    # Emit click event
                    event = ClickElementEvent(
                        selector=params.selector,
                        index=params.index,
                        coordinates=(params.x, params.y) if params.x and params.y else None
                    )
                    await default_event_bus.emit(event)
                
                return ActionResult(
                    success=clicked,
                    action_type="click",
                    extracted_content="Element clicked successfully" if clicked else "Click failed",
                    execution_time=0.0
                )
                
            except Exception as e:
                return ActionResult(
                    success=False,
                    action_type="click",
                    error=str(e),
                    execution_time=0.0
                )
        
        # Type Actions
        @self.registry.action(
            "Type text into an input element",
            param_model=TypeAction,
            category="interaction",
            requires_browser=True,
            requires_dom=True
        )
        async def type_text(params: TypeAction, browser_manager: BrowserManager, dom_service: DOMService):
            try:
                page = browser_manager.get_current_page()
                if not page:
                    raise ValueError("No active page available")
                
                typed = False
                
                # Type by selector
                if params.selector:
                    if params.clear_first:
                        await page.fill(params.selector, params.text)
                    else:
                        await page.type(params.selector, params.text)
                    typed = True
                
                # Type by interactive index
                elif params.index is not None:
                    element = await dom_service.get_element_by_index(params.index)
                    if element and element.selector:
                        if params.clear_first:
                            await page.fill(element.selector, params.text)
                        else:
                            await page.type(element.selector, params.text)
                        typed = True
                    else:
                        raise ValueError(f"No typeable element found with index {params.index}")
                
                else:
                    raise ValueError("Must provide selector or index")
                
                if typed:
                    # Emit type event
                    event = TypeTextEvent(
                        text=params.text,
                        selector=params.selector,
                        index=params.index
                    )
                    await default_event_bus.emit(event)
                
                return ActionResult(
                    success=typed,
                    action_type="type",
                    extracted_content=f"Typed: {params.text}" if typed else "Type failed",
                    execution_time=0.0
                )
                
            except Exception as e:
                return ActionResult(
                    success=False,
                    action_type="type",
                    error=str(e),
                    execution_time=0.0
                )
        
        # Google Search Action
        @self.registry.action(
            "Search Google for a query",
            param_model=SearchGoogleAction,
            category="search",
            requires_browser=True
        )
        async def search_google(params: SearchGoogleAction, browser_manager: BrowserManager):
            try:
                search_url = f"https://www.google.com/search?q={quote_plus(params.query)}"
                await browser_manager.navigate_to(search_url)
                
                return ActionResult(
                    success=True,
                    action_type="search_google",
                    extracted_content=f"Searched Google for: {params.query}",
                    execution_time=0.0
                )
                
            except Exception as e:
                return ActionResult(
                    success=False,
                    action_type="search_google",
                    error=str(e),
                    execution_time=0.0
                )
        
        # Wait Action
        @self.registry.action(
            "Wait for an element to appear or become visible",
            param_model=WaitAction,
            category="utility",
            requires_browser=True
        )
        async def wait_for_element(params: WaitAction, browser_manager: BrowserManager):
            try:
                page = browser_manager.get_current_page()
                if not page:
                    raise ValueError("No active page available")
                
                if params.selector:
                    if params.visible:
                        await page.wait_for_selector(params.selector, timeout=params.timeout * 1000)
                    else:
                        await page.wait_for_selector(
                            params.selector, 
                            state="attached", 
                            timeout=params.timeout * 1000
                        )
                else:
                    # Just wait for the specified time
                    await asyncio.sleep(params.timeout)
                
                return ActionResult(
                    success=True,
                    action_type="wait",
                    extracted_content=f"Wait completed",
                    execution_time=params.timeout
                )
                
            except Exception as e:
                return ActionResult(
                    success=False,
                    action_type="wait",
                    error=str(e),
                    execution_time=0.0
                )
    
    def _register_done_action(self, output_model: type[T]):
        """Register the done action with structured output."""
        
        @self.registry.action(
            "Mark the task as complete with results",
            param_model=DoneAction,
            category="control"
        )
        async def done(params: DoneAction):
            return ActionResult(
                success=params.success,
                action_type="done",
                extracted_content=params.result or "Task completed",
                is_done=True,
                execution_time=0.0
            )
    
    def action(self, description: str, **kwargs):
        """Decorator for registering custom actions."""
        return self.registry.action(description, **kwargs)
    
    async def execute_action(self, action_name: str, params: Dict[str, Any]) -> ActionResult:
        """Execute an action by name."""
        return await self.registry.execute_action(
            action_name=action_name,
            params=params,
            browser_manager=self.browser_manager,
            dom_service=self.dom_service,
            llm_provider=self.llm_provider
        )
    
    def get_available_actions(self, current_url: Optional[str] = None) -> List[str]:
        """Get list of available action descriptions."""
        return self.registry.registry.get_prompt_descriptions(current_url)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        return self.registry.get_stats()
