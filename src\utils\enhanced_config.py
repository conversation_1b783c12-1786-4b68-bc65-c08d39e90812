"""
Enhanced configuration management based on browser-use patterns.
Production-ready configuration system with environment variable support,
config file management, and proper defaults.
"""

import json
import os
import logging
from datetime import datetime
from functools import cache
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from uuid import uuid4

import psutil
from pydantic import BaseModel, Field, ConfigDict
from pydantic_settings import BaseSettings
from pydantic_settings import SettingsConfigDict

logger = logging.getLogger(__name__)


@cache
def is_running_in_docker() -> bool:
    """Detect if we are running in a docker container."""
    try:
        if Path('/.dockerenv').exists() or 'docker' in Path('/proc/1/cgroup').read_text().lower():
            return True
    except Exception:
        pass

    try:
        # Check if init process looks like a container process
        init_cmd = ' '.join(psutil.Process(1).cmdline())
        if ('py' in init_cmd) or ('uv' in init_cmd) or ('app' in init_cmd):
            return True
    except Exception:
        pass

    try:
        # If less than 10 total running processes, likely in container
        if len(psutil.pids()) < 10:
            return True
    except Exception:
        pass

    return False


class DBStyleEntry(BaseModel):
    """Database-style entry with UUID and metadata."""
    
    id: str = Field(default_factory=lambda: str(uuid4()))
    default: bool = Field(default=False)
    created_at: str = Field(default_factory=lambda: datetime.utcnow().isoformat())


class BrowserProfileEntry(DBStyleEntry):
    """Browser profile configuration entry."""
    
    model_config = ConfigDict(extra='allow')
    
    # Basic browser settings
    headless: bool = Field(default=False)
    user_data_dir: Optional[str] = Field(default=None)
    allowed_domains: Optional[List[str]] = Field(default=None)
    downloads_path: Optional[str] = Field(default=None)
    
    # Viewport settings
    viewport_width: int = Field(default=1920)
    viewport_height: int = Field(default=1080)
    
    # Performance settings
    timeout: int = Field(default=30000)
    page_load_timeout: int = Field(default=30000)
    max_concurrent_pages: int = Field(default=5)
    
    # Stealth settings
    disable_blink_features: bool = Field(default=True)
    disable_extensions: bool = Field(default=False)
    disable_plugins: bool = Field(default=True)
    disable_dev_shm_usage: Optional[bool] = Field(default=None)
    disable_gpu: Optional[bool] = Field(default=None)
    no_sandbox: Optional[bool] = Field(default=None)
    
    # Proxy settings
    proxy_server: Optional[str] = Field(default=None)
    proxy_bypass: Optional[str] = Field(default=None)
    proxy_username: Optional[str] = Field(default=None)
    proxy_password: Optional[str] = Field(default=None)
    
    def get_chrome_args(self) -> List[str]:
        """Get Chrome launch arguments based on configuration."""
        args = []
        
        # Auto-configure for Docker environment
        if is_running_in_docker():
            if self.disable_dev_shm_usage is None:
                self.disable_dev_shm_usage = True
            if self.disable_gpu is None:
                self.disable_gpu = True
            if self.no_sandbox is None:
                self.no_sandbox = True
        
        if self.disable_blink_features:
            args.append("--disable-blink-features=AutomationControlled")
        
        if self.disable_extensions:
            args.append("--disable-extensions")
        
        if self.disable_plugins:
            args.append("--disable-plugins")
        
        if self.disable_dev_shm_usage:
            args.append("--disable-dev-shm-usage")
        
        if self.disable_gpu:
            args.append("--disable-gpu")
        
        if self.no_sandbox:
            args.append("--no-sandbox")
        
        if self.proxy_server:
            args.append(f"--proxy-server={self.proxy_server}")
        
        if self.proxy_bypass:
            args.append(f"--proxy-bypass-list={self.proxy_bypass}")
        
        # Additional stealth arguments
        args.extend([
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-extensions-http-throttling",
            "--disable-component-extensions-with-background-pages",
            "--disable-default-apps",
            "--disable-sync",
            "--no-default-browser-check",
            "--no-first-run",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
        ])
        
        return args


class LLMEntry(DBStyleEntry):
    """LLM configuration entry."""
    
    provider: str = Field(default="openai")
    model: str = Field(default="gpt-4o")
    api_key: Optional[str] = Field(default=None)
    temperature: float = Field(default=0.1)
    max_tokens: Optional[int] = Field(default=None)
    timeout: int = Field(default=60)


class AgentEntry(DBStyleEntry):
    """Agent configuration entry."""
    
    max_steps: int = Field(default=10)
    use_vision: bool = Field(default=True)
    vision_detail_level: str = Field(default="auto")
    max_failures: int = Field(default=3)
    use_thinking: bool = Field(default=True)
    flash_mode: bool = Field(default=False)
    max_actions_per_step: int = Field(default=4)
    step_timeout: int = Field(default=180)
    system_prompt: Optional[str] = Field(default=None)


class DBStyleConfigJSON(BaseModel):
    """Database-style configuration format."""
    
    browser_profile: Dict[str, BrowserProfileEntry] = Field(default_factory=dict)
    llm: Dict[str, LLMEntry] = Field(default_factory=dict)
    agent: Dict[str, AgentEntry] = Field(default_factory=dict)


class FlatEnvConfig(BaseSettings):
    """All environment variables in a flat namespace."""
    
    model_config = SettingsConfigDict(
        env_file='.env', 
        env_file_encoding='utf-8', 
        case_sensitive=True, 
        extra='allow'
    )
    
    # Logging and telemetry
    BROWSER_USE_LOGGING_LEVEL: str = Field(default='info')
    BROWSER_USE_DEBUG_LOG_FILE: Optional[str] = Field(default=None)
    BROWSER_USE_INFO_LOG_FILE: Optional[str] = Field(default=None)
    ANONYMIZED_TELEMETRY: bool = Field(default=True)
    
    # Path configuration
    XDG_CACHE_HOME: str = Field(default='~/.cache')
    XDG_CONFIG_HOME: str = Field(default='~/.config')
    BROWSER_USE_CONFIG_DIR: Optional[str] = Field(default=None)
    
    # LLM API keys
    OPENAI_API_KEY: str = Field(default='')
    ANTHROPIC_API_KEY: str = Field(default='')
    GOOGLE_API_KEY: str = Field(default='')
    DEFAULT_LLM: str = Field(default='')
    
    # Browser settings
    BROWSER_USE_HEADLESS: Optional[bool] = Field(default=None)
    BROWSER_USE_ALLOWED_DOMAINS: Optional[str] = Field(default=None)
    BROWSER_USE_LLM_MODEL: Optional[str] = Field(default=None)
    
    # Proxy settings
    BROWSER_USE_PROXY_URL: Optional[str] = Field(default=None)
    BROWSER_USE_NO_PROXY: Optional[str] = Field(default=None)
    BROWSER_USE_PROXY_USERNAME: Optional[str] = Field(default=None)
    BROWSER_USE_PROXY_PASSWORD: Optional[str] = Field(default=None)
    
    # Runtime hints
    IN_DOCKER: Optional[bool] = Field(default=None)
    IS_IN_EVALS: bool = Field(default=False)


def create_default_config() -> DBStyleConfigJSON:
    """Create a fresh default configuration."""
    logger.debug('Creating fresh default config.json')
    
    new_config = DBStyleConfigJSON()
    
    # Generate default IDs
    profile_id = str(uuid4())
    llm_id = str(uuid4())
    agent_id = str(uuid4())
    
    # Create default browser profile entry
    new_config.browser_profile[profile_id] = BrowserProfileEntry(
        id=profile_id, 
        default=True, 
        headless=False, 
        user_data_dir=None
    )
    
    # Create default LLM entry
    new_config.llm[llm_id] = LLMEntry(
        id=llm_id, 
        default=True, 
        provider='gemini',
        model='gemini-2.0-flash-exp', 
        api_key=os.getenv('GOOGLE_API_KEY', '')
    )
    
    # Create default agent entry
    new_config.agent[agent_id] = AgentEntry(
        id=agent_id, 
        default=True
    )
    
    return new_config


def load_and_migrate_config(config_path: Path) -> DBStyleConfigJSON:
    """Load config.json or create fresh one if old format detected."""
    if not config_path.exists():
        # Create fresh config with defaults
        config_path.parent.mkdir(parents=True, exist_ok=True)
        new_config = create_default_config()
        with open(config_path, 'w') as f:
            json.dump(new_config.model_dump(), f, indent=2)
        return new_config
    
    try:
        with open(config_path) as f:
            data = json.load(f)
        
        # Check if it's already in DB-style format
        if all(key in data for key in ['browser_profile', 'llm', 'agent']):
            if data.get('browser_profile') and all(
                isinstance(v, dict) and 'id' in v 
                for v in data['browser_profile'].values()
            ):
                # Already in new format
                return DBStyleConfigJSON(**data)
        
        # Old format detected - create fresh config
        logger.debug(f'Old config format detected at {config_path}, creating fresh config')
        new_config = create_default_config()
        
        # Overwrite with new config
        with open(config_path, 'w') as f:
            json.dump(new_config.model_dump(), f, indent=2)
        
        return new_config
    
    except Exception as e:
        logger.error(f'Failed to load config from {config_path}: {e}, creating fresh config')
        # On any error, create fresh config
        new_config = create_default_config()
        try:
            with open(config_path, 'w') as f:
                json.dump(new_config.model_dump(), f, indent=2)
        except Exception as write_error:
            logger.error(f'Failed to write fresh config: {write_error}')
        return new_config


class EnhancedConfig:
    """Enhanced configuration class that merges all config sources."""
    
    def __init__(self):
        self._dirs_created = False
    
    def _get_config_path(self) -> Path:
        """Get config path from environment."""
        env_config = FlatEnvConfig()
        if env_config.BROWSER_USE_CONFIG_DIR:
            return Path(env_config.BROWSER_USE_CONFIG_DIR).expanduser() / 'config.json'
        else:
            xdg_config = Path(env_config.XDG_CONFIG_HOME).expanduser()
            return xdg_config / 'browseruse' / 'config.json'
    
    def _get_db_config(self) -> DBStyleConfigJSON:
        """Load and migrate config.json."""
        config_path = self._get_config_path()
        return load_and_migrate_config(config_path)
    
    def get_default_profile(self) -> Dict[str, Any]:
        """Get the default browser profile configuration."""
        db_config = self._get_db_config()
        for profile in db_config.browser_profile.values():
            if profile.default:
                return profile.model_dump(exclude_none=True)
        
        # Return first profile if no default
        if db_config.browser_profile:
            return next(iter(db_config.browser_profile.values())).model_dump(exclude_none=True)
        
        return {}
    
    def get_default_llm(self) -> Dict[str, Any]:
        """Get the default LLM configuration."""
        db_config = self._get_db_config()
        for llm in db_config.llm.values():
            if llm.default:
                return llm.model_dump(exclude_none=True)
        
        # Return first LLM if no default
        if db_config.llm:
            return next(iter(db_config.llm.values())).model_dump(exclude_none=True)
        
        return {}
    
    def get_default_agent(self) -> Dict[str, Any]:
        """Get the default agent configuration."""
        db_config = self._get_db_config()
        for agent in db_config.agent.values():
            if agent.default:
                return agent.model_dump(exclude_none=True)
        
        # Return first agent if no default
        if db_config.agent:
            return next(iter(db_config.agent.values())).model_dump(exclude_none=True)
        
        return {}
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration with env var overrides."""
        config = {
            'browser_profile': self.get_default_profile(),
            'llm': self.get_default_llm(),
            'agent': self.get_default_agent(),
        }
        
        # Apply environment variable overrides
        env_config = FlatEnvConfig()
        
        if env_config.BROWSER_USE_HEADLESS is not None:
            config['browser_profile']['headless'] = env_config.BROWSER_USE_HEADLESS
        
        if env_config.BROWSER_USE_ALLOWED_DOMAINS:
            domains = [d.strip() for d in env_config.BROWSER_USE_ALLOWED_DOMAINS.split(',') if d.strip()]
            config['browser_profile']['allowed_domains'] = domains
        
        # Proxy settings
        proxy_dict = {}
        if env_config.BROWSER_USE_PROXY_URL:
            proxy_dict['server'] = env_config.BROWSER_USE_PROXY_URL
        if env_config.BROWSER_USE_NO_PROXY:
            proxy_dict['bypass'] = ','.join([d.strip() for d in env_config.BROWSER_USE_NO_PROXY.split(',') if d.strip()])
        if env_config.BROWSER_USE_PROXY_USERNAME:
            proxy_dict['username'] = env_config.BROWSER_USE_PROXY_USERNAME
        if env_config.BROWSER_USE_PROXY_PASSWORD:
            proxy_dict['password'] = env_config.BROWSER_USE_PROXY_PASSWORD
        if proxy_dict:
            config.setdefault('browser_profile', {})
            config['browser_profile']['proxy'] = proxy_dict
        
        # LLM overrides
        if env_config.OPENAI_API_KEY:
            config['llm']['api_key'] = env_config.OPENAI_API_KEY
        if env_config.GOOGLE_API_KEY:
            config['llm']['api_key'] = env_config.GOOGLE_API_KEY
        if env_config.ANTHROPIC_API_KEY:
            config['llm']['api_key'] = env_config.ANTHROPIC_API_KEY
        
        if env_config.BROWSER_USE_LLM_MODEL:
            config['llm']['model'] = env_config.BROWSER_USE_LLM_MODEL
        
        return config


# Create singleton instance
ENHANCED_CONFIG = EnhancedConfig()
