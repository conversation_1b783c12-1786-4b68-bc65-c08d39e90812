"""
Enhanced logging configuration for the AI Sourcing Agent.
Based on browser-use patterns for production reliability.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import json
from datetime import datetime

from dotenv import load_dotenv

load_dotenv()

# Configuration will be imported when needed to avoid circular imports


def addLoggingLevel(levelName, levelNum, methodName=None):
    """
    Comprehensively adds a new logging level to the `logging` module and the
    currently configured logging class.

    Based on browser-use implementation for custom log levels.
    """
    if not methodName:
        methodName = levelName.lower()

    if hasattr(logging, levelName):
        raise AttributeError(f'{levelName} already defined in logging module')
    if hasattr(logging, methodName):
        raise AttributeError(f'{methodName} already defined in logging module')
    if hasattr(logging.getLoggerClass(), methodName):
        raise AttributeError(f'{methodName} already defined in logger class')

    def logForLevel(self, message, *args, **kwargs):
        if self.isEnabledFor(levelNum):
            self._log(levelNum, message, args, **kwargs)

    def logToRoot(message, *args, **kwargs):
        logging.log(levelNum, message, *args, **kwargs)

    logging.addLevelName(levelNum, levelName)
    setattr(logging, levelName, levelNum)
    setattr(logging.getLoggerClass(), methodName, logForLevel)
    setattr(logging, methodName, logToRoot)


def setup_logging(stream=None, log_level=None, force_setup=False, debug_log_file=None, info_log_file=None):
    """Setup logging configuration for AI Sourcing Agent.

    Args:
        stream: Output stream for logs (default: sys.stdout)
        log_level: Override log level (default: uses BROWSER_USE_LOGGING_LEVEL env var or 'info')
        force_setup: Force reconfiguration even if handlers already exist
        debug_log_file: Path to log file for debug level logs only
        info_log_file: Path to log file for info level logs only
    """
    # Try to add RESULT level, but ignore if it already exists
    try:
        addLoggingLevel('RESULT', 35)  # Between WARNING and ERROR
    except AttributeError:
        pass  # Level already exists, which is fine

    # Get log level from environment or default to 'info'
    log_type = log_level or os.getenv('BROWSER_USE_LOGGING_LEVEL', 'info').lower()

    # Check if handlers are already set up
    if logging.getLogger().hasHandlers() and not force_setup:
        return logging.getLogger('ai_sourcing_agent')

    # Clear existing handlers
    root = logging.getLogger()
    root.handlers = []

    class AISourceFormatter(logging.Formatter):
        def __init__(self, fmt, log_level):
            super().__init__(fmt)
            self.log_level = log_level

        def format(self, record):
            # Only clean up names in INFO mode, keep everything in DEBUG mode
            if self.log_level > logging.DEBUG and isinstance(record.name, str) and record.name.startswith('src.'):
                # Extract clean component names from logger names
                if 'agent' in record.name:
                    record.name = 'Agent'
                elif 'browser' in record.name:
                    record.name = 'Browser'
                elif 'tools' in record.name:
                    record.name = 'Tools'
                elif 'llm' in record.name:
                    record.name = 'LLM'
                elif record.name.startswith('src.'):
                    # For other modules, use the last part
                    parts = record.name.split('.')
                    if len(parts) >= 2:
                        record.name = parts[-1]
            return super().format(record)


    # Setup single handler for all loggers
    console = logging.StreamHandler(stream or sys.stdout)

    # Determine the log level to use first
    if log_type == 'result':
        log_level = 35  # RESULT level value
    elif log_type == 'debug':
        log_level = logging.DEBUG
    else:
        log_level = logging.INFO

    # Additional setLevel here to filter logs
    if log_type == 'result':
        console.setLevel('RESULT')
        console.setFormatter(AISourceFormatter('%(message)s', log_level))
    else:
        console.setLevel(log_level)
        console.setFormatter(AISourceFormatter('%(levelname)-8s [%(name)s] %(message)s', log_level))

    # Configure root logger only
    root.addHandler(console)

    # Add file handlers if specified
    file_handlers = []

    # Create debug log file handler
    if debug_log_file:
        debug_handler = logging.FileHandler(debug_log_file, encoding='utf-8')
        debug_handler.setLevel(logging.DEBUG)
        debug_handler.setFormatter(AISourceFormatter('%(asctime)s - %(levelname)-8s [%(name)s] %(message)s', logging.DEBUG))
        file_handlers.append(debug_handler)
        root.addHandler(debug_handler)

    # Create info log file handler
    if info_log_file:
        info_handler = logging.FileHandler(info_log_file, encoding='utf-8')
        info_handler.setLevel(logging.INFO)
        info_handler.setFormatter(AISourceFormatter('%(asctime)s - %(levelname)-8s [%(name)s] %(message)s', logging.INFO))
        file_handlers.append(info_handler)
        root.addHandler(info_handler)

    # Configure root logger - use DEBUG if debug file logging is enabled
    effective_log_level = logging.DEBUG if debug_log_file else log_level
    root.setLevel(effective_log_level)

    # Configure ai_sourcing_agent logger
    ai_logger = logging.getLogger('ai_sourcing_agent')
    ai_logger.propagate = False  # Don't propagate to root logger
    ai_logger.addHandler(console)
    for handler in file_handlers:
        ai_logger.addHandler(handler)
    ai_logger.setLevel(effective_log_level)

    logger = logging.getLogger('ai_sourcing_agent')

    # Silence third-party loggers
    third_party_loggers = [
        'WDM',
        'httpx',
        'selenium',
        'playwright',
        'urllib3',
        'asyncio',
        'openai',
        'httpcore',
        'charset_normalizer',
        'anthropic._base_client',
        'PIL.PngImagePlugin',
        'groq',
        'portalocker',
        'google_genai',
        'portalocker.utils',
        'websockets',
    ]
    for logger_name in third_party_loggers:
        third_party = logging.getLogger(logger_name)
        third_party.setLevel(logging.ERROR)
        third_party.propagate = False

    return logger


def get_logger(name: str, level: str = "INFO") -> logging.Logger:
    """Get a configured logger instance."""
    logger = logging.getLogger(name)

    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, level.upper()))

    return logger


class ScreenshotLogger:
    """Handles screenshot logging and management."""

    def __init__(self, screenshot_dir: str = None):
        self.screenshot_dir = Path(screenshot_dir or "logs/screenshots")
        self.screenshot_dir.mkdir(parents=True, exist_ok=True)

    def save_screenshot(self, screenshot_data: bytes, filename: str = None) -> str:
        """Save screenshot data to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = f"screenshot_{timestamp}.png"

        filepath = self.screenshot_dir / filename
        with open(filepath, "wb") as f:
            f.write(screenshot_data)

        return str(filepath)

    def cleanup_old_screenshots(self, max_age_days: int = 7):
        """Remove screenshots older than specified days."""
        import time
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60

        for screenshot_file in self.screenshot_dir.glob("*.png"):
            if current_time - screenshot_file.stat().st_mtime > max_age_seconds:
                screenshot_file.unlink()


class AgentLogger:
    """Specialized logger for agent operations."""

    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.logger = get_logger(f"agent.{agent_id}")
        self.screenshot_logger = ScreenshotLogger()

    def info(self, message: str, **kwargs):
        """Log info message with agent context."""
        self.logger.info(f"[{self.agent_id}] {message}", extra=kwargs)

    def debug(self, message: str, **kwargs):
        """Log debug message with agent context."""
        self.logger.debug(f"[{self.agent_id}] {message}", extra=kwargs)

    def warning(self, message: str, **kwargs):
        """Log warning message with agent context."""
        self.logger.warning(f"[{self.agent_id}] {message}", extra=kwargs)

    def error(self, message: str, **kwargs):
        """Log error message with agent context."""
        self.logger.error(f"[{self.agent_id}] {message}", extra=kwargs)

    def log_task_start(self, task: str, task_id: str = None):
        """Log the start of a task."""
        self.info(f"Task started: {task}", task_id=task_id, event="task_start")

    def log_task_complete(self, task: str, task_id: str = None, duration: float = None):
        """Log the completion of a task."""
        self.info(f"Task completed: {task}", task_id=task_id, duration=duration, event="task_complete")

    def log_browser_action(self, action: str, element: str = None, value: str = None):
        """Log a browser action."""
        self.debug(f"Browser action: {action}", element=element, value=value, event="browser_action")

    def log_screenshot(self, screenshot_data: bytes, context: str = None) -> Optional[str]:
        """Log a screenshot with context."""
        screenshot_path = self.screenshot_logger.save_screenshot(screenshot_data)
        self.info(f"Screenshot captured: {context or 'No context'}", screenshot_path=screenshot_path, event="screenshot")
        return screenshot_path

    def log_error_with_screenshot(self, error: Exception, screenshot_data: bytes = None):
        """Log an error with optional screenshot."""
        screenshot_path = None
        if screenshot_data:
            screenshot_path = self.screenshot_logger.save_screenshot(screenshot_data)

        self.error(
            f"Agent error: {str(error)}",
            error_type=type(error).__name__,
            screenshot_path=screenshot_path,
            event="error",
            exc_info=True
        )
