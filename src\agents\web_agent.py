"""
Web automation agent implementation.
"""

import asyncio
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from .base_agent import BaseAgent
from ..utils import get_logger, ElementNotFoundError, TimeoutError, retry_on_exception
from ..llm.prompt_templates import PromptTemplates
from ..tools import BrowserTools


class WebAgent(BaseAgent):
    """Specialized agent for web browser automation tasks."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.tools = None
        self.prompt_templates = PromptTemplates()
        
        # Web-specific configuration
        self.max_retries = kwargs.get("max_retries", 3)
        self.element_timeout = kwargs.get("element_timeout", 10000)
        self.page_load_timeout = kwargs.get("page_load_timeout", 30000)
        
        # Task execution context
        self.execution_context = {
            "current_url": "",
            "page_title": "",
            "last_action": "",
            "action_history": []
        }
    
    async def initialize(self) -> bool:
        """Initialize the web agent."""
        success = await super().initialize()
        if success:
            # Initialize browser tools
            self.tools = BrowserTools(self.browser_manager, self.logger)
            await self.tools.initialize()
        return success
    
    async def execute_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Execute a web automation task."""
        try:
            self.logger.info(f"Executing web task: {task}")
            
            # Update execution context
            await self._update_execution_context()
            
            # Determine task type and execute accordingly
            task_type = self._classify_task(task)
            
            if task_type == "navigation":
                return await self._handle_navigation_task(task, **kwargs)
            elif task_type == "interaction":
                return await self._handle_interaction_task(task, **kwargs)
            elif task_type == "data_extraction":
                return await self._handle_data_extraction_task(task, **kwargs)
            elif task_type == "form_filling":
                return await self._handle_form_filling_task(task, **kwargs)
            else:
                return await self._handle_general_task(task, **kwargs)
                
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            raise
    
    def _classify_task(self, task: str) -> str:
        """Classify the type of task based on keywords."""
        task_lower = task.lower()
        
        if any(keyword in task_lower for keyword in ["navigate", "go to", "visit", "open"]):
            return "navigation"
        elif any(keyword in task_lower for keyword in ["click", "type", "select", "scroll"]):
            return "interaction"
        elif any(keyword in task_lower for keyword in ["extract", "get", "find", "scrape"]):
            return "data_extraction"
        elif any(keyword in task_lower for keyword in ["fill", "submit", "form", "login"]):
            return "form_filling"
        else:
            return "general"
    
    async def _update_execution_context(self):
        """Update the current execution context."""
        try:
            page = await self.browser_manager.get_page()
            self.execution_context.update({
                "current_url": await self.browser_manager.get_current_url(page),
                "page_title": await self.browser_manager.get_page_title(page),
                "timestamp": datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.warning(f"Failed to update execution context: {e}")
    
    async def _handle_navigation_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle navigation-specific tasks."""
        try:
            # Extract URL from task or kwargs
            target_url = kwargs.get("url")
            
            if not target_url:
                # Use LLM to extract URL from natural language
                prompt = self.prompt_templates.navigation_prompt(
                    navigation_instruction=task,
                    current_url=self.execution_context.get("current_url")
                )
                
                response = await self.llm.generate_structured_response(
                    prompt,
                    schema={
                        "action": "string",
                        "url": "string",
                        "reasoning": "string"
                    }
                )
                
                target_url = response.get("url")
            
            if not target_url:
                raise ValueError("Could not determine target URL for navigation")
            
            # Perform navigation
            success = await self.browser_manager.navigate(target_url)
            
            if success:
                await self._update_execution_context()
                self.execution_context["last_action"] = f"Navigated to {target_url}"
                
                return {
                    "success": True,
                    "action": "navigation",
                    "target_url": target_url,
                    "current_url": self.execution_context["current_url"],
                    "page_title": self.execution_context["page_title"]
                }
            else:
                raise Exception(f"Navigation to {target_url} failed")
                
        except Exception as e:
            self.logger.error(f"Navigation task failed: {e}")
            raise
    
    async def _handle_interaction_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle element interaction tasks."""
        try:
            # Get current page state
            page = await self.browser_manager.get_page()
            page_content = await self.browser_manager.get_page_content(page)
            
            # Use LLM to determine the interaction
            prompt = self.prompt_templates.browser_task_prompt(
                task=task,
                current_url=self.execution_context.get("current_url"),
                page_content=page_content[:3000],  # Limit content size
                available_tools=self.tools.get_available_tools(),
                context=self.execution_context
            )
            
            response = await self.llm.generate_structured_response(
                prompt,
                schema={
                    "action": "string",
                    "element_selector": "string",
                    "value": "string",
                    "reasoning": "string",
                    "steps": "array"
                }
            )
            
            # Execute the planned action
            action = response.get("action", "").lower()
            selector = response.get("element_selector")
            value = response.get("value")
            
            result = None
            if action == "click":
                result = await self.tools.click_element(selector)
            elif action == "type":
                result = await self.tools.type_text(selector, value)
            elif action == "scroll":
                result = await self.tools.scroll_page(direction=value or "down")
            elif action == "select":
                result = await self.tools.select_option(selector, value)
            else:
                raise ValueError(f"Unknown action: {action}")
            
            # Update context
            self.execution_context["last_action"] = f"{action} on {selector}"
            self.execution_context["action_history"].append({
                "action": action,
                "selector": selector,
                "value": value,
                "timestamp": datetime.now().isoformat(),
                "result": result
            })
            
            return {
                "success": True,
                "action": action,
                "element_selector": selector,
                "value": value,
                "result": result,
                "reasoning": response.get("reasoning")
            }
            
        except Exception as e:
            self.logger.error(f"Interaction task failed: {e}")
            raise
    
    async def _handle_data_extraction_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle data extraction tasks."""
        try:
            # Get current page content
            page = await self.browser_manager.get_page()
            page_content = await self.browser_manager.get_page_content(page)
            
            # Determine what data to extract
            output_format = kwargs.get("format", "json")
            specific_fields = kwargs.get("fields", [])
            
            prompt = self.prompt_templates.data_extraction_prompt(
                extraction_target=task,
                page_content=page_content,
                output_format=output_format,
                specific_fields=specific_fields
            )
            
            # Use LLM to extract data
            if output_format.lower() == "json":
                schema = kwargs.get("schema", {
                    "extracted_data": "object",
                    "source_url": "string",
                    "extraction_timestamp": "string"
                })
                
                response = await self.llm.generate_structured_response(prompt, schema)
            else:
                response = await self.llm.generate_response(prompt)
            
            # Update context
            self.execution_context["last_action"] = f"Extracted data: {task}"
            
            return {
                "success": True,
                "action": "data_extraction",
                "extracted_data": response,
                "source_url": self.execution_context.get("current_url"),
                "extraction_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Data extraction task failed: {e}")
            raise
    
    async def _handle_form_filling_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle form filling tasks."""
        try:
            form_data = kwargs.get("form_data", {})
            if not form_data:
                raise ValueError("Form data is required for form filling tasks")
            
            # Get current page content
            page = await self.browser_manager.get_page()
            page_content = await self.browser_manager.get_page_content(page)
            
            prompt = self.prompt_templates.form_filling_prompt(
                form_data=form_data,
                form_description=task,
                page_content=page_content[:3000]
            )
            
            # Use LLM to plan form filling
            response = await self.llm.generate_structured_response(
                prompt,
                schema={
                    "form_fields": "array",
                    "submit_button": "string",
                    "steps": "array",
                    "reasoning": "string"
                }
            )
            
            # Execute form filling steps
            results = []
            for field_info in response.get("form_fields", []):
                field_selector = field_info.get("selector")
                field_value = field_info.get("value")
                field_type = field_info.get("type", "text")
                
                if field_type == "text" or field_type == "email" or field_type == "password":
                    result = await self.tools.type_text(field_selector, field_value)
                elif field_type == "select":
                    result = await self.tools.select_option(field_selector, field_value)
                elif field_type == "checkbox":
                    result = await self.tools.click_element(field_selector)
                
                results.append({
                    "field": field_selector,
                    "value": field_value,
                    "type": field_type,
                    "result": result
                })
            
            # Submit form if requested
            submit_result = None
            if kwargs.get("submit", False) and response.get("submit_button"):
                submit_result = await self.tools.click_element(response["submit_button"])
            
            # Update context
            self.execution_context["last_action"] = f"Filled form with {len(results)} fields"
            
            return {
                "success": True,
                "action": "form_filling",
                "fields_filled": len(results),
                "field_results": results,
                "submit_result": submit_result,
                "reasoning": response.get("reasoning")
            }
            
        except Exception as e:
            self.logger.error(f"Form filling task failed: {e}")
            raise
    
    async def _handle_general_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle general tasks using LLM planning."""
        try:
            # Get current page state
            page = await self.browser_manager.get_page()
            page_content = await self.browser_manager.get_page_content(page)
            
            # Take screenshot for visual context
            screenshot_path = await self.browser_manager.take_screenshot(page)
            
            # Use LLM to plan and execute the task
            prompt = self.prompt_templates.task_planning_prompt(
                high_level_task=task,
                available_tools=self.tools.get_available_tools(),
                context={
                    "current_url": self.execution_context.get("current_url"),
                    "page_title": self.execution_context.get("page_title"),
                    "screenshot_available": screenshot_path is not None
                }
            )
            
            response = await self.llm.generate_structured_response(
                prompt,
                schema={
                    "task_plan": "array",
                    "expected_outcome": "string",
                    "reasoning": "string"
                }
            )
            
            # Execute the planned steps
            step_results = []
            for step in response.get("task_plan", []):
                step_action = step.get("action")
                step_params = step.get("parameters", {})
                
                try:
                    # Execute step based on action type
                    if step_action == "navigate":
                        result = await self.browser_manager.navigate(step_params.get("url"))
                    elif step_action == "click":
                        result = await self.tools.click_element(step_params.get("selector"))
                    elif step_action == "type":
                        result = await self.tools.type_text(
                            step_params.get("selector"),
                            step_params.get("text")
                        )
                    elif step_action == "wait":
                        await asyncio.sleep(step_params.get("seconds", 1))
                        result = True
                    else:
                        result = f"Unknown action: {step_action}"
                    
                    step_results.append({
                        "step": step,
                        "result": result,
                        "success": True
                    })
                    
                except Exception as step_error:
                    step_results.append({
                        "step": step,
                        "error": str(step_error),
                        "success": False
                    })
            
            # Update context
            self.execution_context["last_action"] = f"Executed general task: {task}"
            
            return {
                "success": True,
                "action": "general_task",
                "task_plan": response.get("task_plan"),
                "step_results": step_results,
                "expected_outcome": response.get("expected_outcome"),
                "reasoning": response.get("reasoning")
            }
            
        except Exception as e:
            self.logger.error(f"General task failed: {e}")
            raise
    
    async def get_execution_context(self) -> Dict[str, Any]:
        """Get the current execution context."""
        await self._update_execution_context()
        return self.execution_context.copy()
    
    async def clear_execution_context(self):
        """Clear the execution context."""
        self.execution_context = {
            "current_url": "",
            "page_title": "",
            "last_action": "",
            "action_history": []
        }
