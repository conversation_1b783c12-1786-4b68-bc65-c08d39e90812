"""
Enhanced observability service based on browser-use patterns.
Provides comprehensive logging, metrics, tracing, and telemetry collection.
"""

import asyncio
import logging
import os
import time
from collections.abc import Callable
from functools import wraps
from typing import Any, Dict, List, Optional, Literal, TypeVar, cast
from uuid import uuid4
from datetime import datetime
from pathlib import Path

from pydantic import BaseModel, Field

from ..utils import get_logger
from ..config import config_service

logger = get_logger(__name__)

F = TypeVar('F', bound=Callable[..., Any])


class MetricEvent(BaseModel):
    """Metric event data."""
    
    name: str = Field(..., description="Metric name")
    value: float = Field(..., description="Metric value")
    unit: str = Field("count", description="Metric unit")
    tags: Dict[str, str] = Field(default_factory=dict, description="Metric tags")
    timestamp: float = Field(default_factory=time.time, description="Event timestamp")


class TraceSpan(BaseModel):
    """Trace span data."""
    
    span_id: str = Field(default_factory=lambda: str(uuid4()), description="Span ID")
    trace_id: str = Field(..., description="Trace ID")
    parent_span_id: Optional[str] = Field(None, description="Parent span ID")
    
    name: str = Field(..., description="Span name")
    operation: str = Field(..., description="Operation name")
    start_time: float = Field(default_factory=time.time, description="Start timestamp")
    end_time: Optional[float] = Field(None, description="End timestamp")
    duration: Optional[float] = Field(None, description="Duration in seconds")
    
    # Span data
    input_data: Optional[Dict[str, Any]] = Field(None, description="Input parameters")
    output_data: Optional[Dict[str, Any]] = Field(None, description="Output data")
    error: Optional[str] = Field(None, description="Error message if failed")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    tags: Dict[str, str] = Field(default_factory=dict, description="Span tags")
    
    def finish(self, output_data: Optional[Dict[str, Any]] = None, error: Optional[str] = None):
        """Finish the span."""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        if output_data:
            self.output_data = output_data
        if error:
            self.error = error


class TelemetryEvent(BaseModel):
    """Telemetry event data."""
    
    event_id: str = Field(default_factory=lambda: str(uuid4()), description="Event ID")
    event_name: str = Field(..., description="Event name")
    event_type: str = Field("custom", description="Event type")
    
    # Event data
    properties: Dict[str, Any] = Field(default_factory=dict, description="Event properties")
    user_id: Optional[str] = Field(None, description="User ID")
    session_id: Optional[str] = Field(None, description="Session ID")
    
    # Timing
    timestamp: float = Field(default_factory=time.time, description="Event timestamp")
    
    # Context
    context: Dict[str, Any] = Field(default_factory=dict, description="Event context")


class ObservabilityService:
    """
    Enhanced observability service based on browser-use patterns.
    
    Features:
    - Distributed tracing
    - Metrics collection
    - Telemetry events
    - Performance monitoring
    - Debug mode support
    """
    
    def __init__(self):
        self.logger = get_logger(f"{self.__class__.__name__}")
        
        # Configuration
        self.config = config_service.get_agent_config()
        self.telemetry_enabled = self.config.enable_telemetry
        self.debug_mode = self._is_debug_mode()
        
        # Storage
        self.metrics: List[MetricEvent] = []
        self.spans: Dict[str, TraceSpan] = {}
        self.telemetry_events: List[TelemetryEvent] = []
        
        # Current trace context
        self.current_trace_id: Optional[str] = None
        self.current_span_id: Optional[str] = None
        
        # Session tracking
        self.session_id = str(uuid4())
        self.start_time = time.time()
        
        self.logger.debug(f"Observability service initialized (telemetry: {self.telemetry_enabled}, debug: {self.debug_mode})")
    
    def _is_debug_mode(self) -> bool:
        """Check if we're in debug mode."""
        # Check environment variables
        debug_vars = ['DEBUG', 'AI_SOURCING_DEBUG']
        for var in debug_vars:
            value = os.getenv(var, '').lower()
            if value in ('1', 'true', 'yes', 'on'):
                return True
        
        # Check logging level
        root_logger = logging.getLogger()
        return root_logger.level <= logging.DEBUG
    
    def start_trace(self, trace_id: Optional[str] = None) -> str:
        """Start a new trace."""
        if trace_id is None:
            trace_id = str(uuid4())
        
        self.current_trace_id = trace_id
        self.logger.debug(f"Started trace: {trace_id}")
        return trace_id
    
    def start_span(
        self,
        name: str,
        operation: str = "operation",
        parent_span_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[Dict[str, str]] = None
    ) -> TraceSpan:
        """Start a new span."""
        if not self.current_trace_id:
            self.start_trace()
        
        span = TraceSpan(
            trace_id=self.current_trace_id,
            parent_span_id=parent_span_id or self.current_span_id,
            name=name,
            operation=operation,
            metadata=metadata or {},
            tags=tags or {}
        )
        
        self.spans[span.span_id] = span
        self.current_span_id = span.span_id
        
        self.logger.debug(f"Started span: {name} ({span.span_id})")
        return span
    
    def finish_span(
        self,
        span_id: str,
        output_data: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ):
        """Finish a span."""
        if span_id in self.spans:
            span = self.spans[span_id]
            span.finish(output_data, error)
            
            # Update current span to parent
            if span.parent_span_id:
                self.current_span_id = span.parent_span_id
            else:
                self.current_span_id = None
            
            self.logger.debug(f"Finished span: {span.name} ({span_id}) - {span.duration:.3f}s")
    
    def record_metric(
        self,
        name: str,
        value: float,
        unit: str = "count",
        tags: Optional[Dict[str, str]] = None
    ):
        """Record a metric."""
        metric = MetricEvent(
            name=name,
            value=value,
            unit=unit,
            tags=tags or {}
        )
        
        self.metrics.append(metric)
        self.logger.debug(f"Recorded metric: {name}={value} {unit}")
    
    def record_telemetry(
        self,
        event_name: str,
        event_type: str = "custom",
        properties: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        """Record a telemetry event."""
        if not self.telemetry_enabled:
            return
        
        event = TelemetryEvent(
            event_name=event_name,
            event_type=event_type,
            properties=properties or {},
            session_id=self.session_id,
            context=context or {}
        )
        
        self.telemetry_events.append(event)
        self.logger.debug(f"Recorded telemetry: {event_name}")
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary."""
        if not self.metrics:
            return {}
        
        summary = {}
        for metric in self.metrics:
            key = f"{metric.name}_{metric.unit}"
            if key not in summary:
                summary[key] = {
                    "count": 0,
                    "sum": 0.0,
                    "min": float('inf'),
                    "max": float('-inf'),
                    "avg": 0.0
                }
            
            summary[key]["count"] += 1
            summary[key]["sum"] += metric.value
            summary[key]["min"] = min(summary[key]["min"], metric.value)
            summary[key]["max"] = max(summary[key]["max"], metric.value)
            summary[key]["avg"] = summary[key]["sum"] / summary[key]["count"]
        
        return summary
    
    def get_trace_summary(self) -> Dict[str, Any]:
        """Get trace summary."""
        if not self.spans:
            return {}
        
        total_spans = len(self.spans)
        completed_spans = sum(1 for span in self.spans.values() if span.end_time is not None)
        failed_spans = sum(1 for span in self.spans.values() if span.error is not None)
        
        durations = [span.duration for span in self.spans.values() if span.duration is not None]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        return {
            "total_spans": total_spans,
            "completed_spans": completed_spans,
            "failed_spans": failed_spans,
            "success_rate": completed_spans / total_spans if total_spans > 0 else 0,
            "average_duration": avg_duration,
            "total_traces": len(set(span.trace_id for span in self.spans.values()))
        }
    
    def export_data(self, format: str = "json") -> Dict[str, Any]:
        """Export observability data."""
        data = {
            "session_id": self.session_id,
            "start_time": self.start_time,
            "export_time": time.time(),
            "metrics": [metric.model_dump() for metric in self.metrics],
            "spans": [span.model_dump() for span in self.spans.values()],
            "telemetry_events": [event.model_dump() for event in self.telemetry_events],
            "summary": {
                "metrics": self.get_metrics_summary(),
                "traces": self.get_trace_summary()
            }
        }
        
        return data
    
    def clear_data(self):
        """Clear all collected data."""
        self.metrics.clear()
        self.spans.clear()
        self.telemetry_events.clear()
        self.current_trace_id = None
        self.current_span_id = None
        
        self.logger.debug("Cleared observability data")


# Global observability service instance
observability_service = ObservabilityService()


def observe(
    name: Optional[str] = None,
    ignore_input: bool = False,
    ignore_output: bool = False,
    metadata: Optional[Dict[str, Any]] = None,
    span_type: Literal['DEFAULT', 'LLM', 'TOOL'] = 'DEFAULT',
    **kwargs: Any,
) -> Callable[[F], F]:
    """
    Observability decorator that traces function execution.
    
    Args:
        name: Name of the span/trace
        ignore_input: Whether to ignore function input parameters in tracing
        ignore_output: Whether to ignore function output in tracing
        metadata: Additional metadata to attach to the span
        span_type: Type of span (DEFAULT, LLM, TOOL)
        **kwargs: Additional parameters
        
    Returns:
        Decorated function that will be traced
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            # Prepare input data
            input_data = None if ignore_input else {
                "args": str(args)[:1000],  # Truncate long args
                "kwargs": {k: str(v)[:1000] for k, v in kwargs.items()}
            }
            
            # Start span
            span = observability_service.start_span(
                name=span_name,
                operation=span_type.lower(),
                metadata=metadata,
                tags={"function": func.__name__, "module": func.__module__}
            )
            
            if input_data:
                span.input_data = input_data
            
            try:
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Prepare output data
                output_data = None if ignore_output else {"result": str(result)[:1000]}
                
                # Finish span
                observability_service.finish_span(span.span_id, output_data)
                
                return result
                
            except Exception as e:
                # Finish span with error
                observability_service.finish_span(span.span_id, error=str(e))
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            # Prepare input data
            input_data = None if ignore_input else {
                "args": str(args)[:1000],
                "kwargs": {k: str(v)[:1000] for k, v in kwargs.items()}
            }
            
            # Start span
            span = observability_service.start_span(
                name=span_name,
                operation=span_type.lower(),
                metadata=metadata,
                tags={"function": func.__name__, "module": func.__module__}
            )
            
            if input_data:
                span.input_data = input_data
            
            try:
                # Execute function
                result = func(*args, **kwargs)
                
                # Prepare output data
                output_data = None if ignore_output else {"result": str(result)[:1000]}
                
                # Finish span
                observability_service.finish_span(span.span_id, output_data)
                
                return result
                
            except Exception as e:
                # Finish span with error
                observability_service.finish_span(span.span_id, error=str(e))
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return cast(F, async_wrapper)
        else:
            return cast(F, sync_wrapper)
    
    return decorator


def observe_debug(
    name: Optional[str] = None,
    ignore_input: bool = False,
    ignore_output: bool = False,
    metadata: Optional[Dict[str, Any]] = None,
    span_type: Literal['DEFAULT', 'LLM', 'TOOL'] = 'DEFAULT',
    **kwargs: Any,
) -> Callable[[F], F]:
    """
    Debug-only observability decorator that only traces when in debug mode.
    
    Args:
        name: Name of the span/trace
        ignore_input: Whether to ignore function input parameters in tracing
        ignore_output: Whether to ignore function output in tracing
        metadata: Additional metadata to attach to the span
        span_type: Type of span (DEFAULT, LLM, TOOL)
        **kwargs: Additional parameters
        
    Returns:
        Decorated function that may be traced only in debug mode
    """
    if observability_service.debug_mode:
        return observe(name, ignore_input, ignore_output, metadata, span_type, **kwargs)
    else:
        # Return no-op decorator
        def decorator(func: F) -> F:
            return func
        return decorator


def time_execution_async(additional_text: str = '') -> Callable:
    """Decorator to time async function execution."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Record metric
            observability_service.record_metric(
                name=f"execution_time_{func.__name__}",
                value=execution_time,
                unit="seconds",
                tags={"function": func.__name__, "module": func.__module__}
            )
            
            # Log if execution takes more than 0.25 seconds
            if execution_time > 0.25:
                logger.debug(f"{func.__name__}{additional_text} took {execution_time:.3f}s")
            
            return result
        return wrapper
    return decorator


def time_execution_sync(additional_text: str = '') -> Callable:
    """Decorator to time sync function execution."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Record metric
            observability_service.record_metric(
                name=f"execution_time_{func.__name__}",
                value=execution_time,
                unit="seconds",
                tags={"function": func.__name__, "module": func.__module__}
            )
            
            # Log if execution takes more than 0.25 seconds
            if execution_time > 0.25:
                logger.debug(f"{func.__name__}{additional_text} took {execution_time:.3f}s")
            
            return result
        return wrapper
    return decorator
