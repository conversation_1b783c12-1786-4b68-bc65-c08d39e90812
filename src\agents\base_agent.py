"""
Base agent class for browser automation.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime

from ..utils import get_logger, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TaskExecutionError, safe_execute_async
from ..llm import create_llm
from ..browser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SessionManager


class BaseAgent(ABC):
    """Abstract base class for browser automation agents."""
    
    def __init__(
        self,
        agent_id: str = None,
        llm_provider: str = None,
        llm_model: str = None,
        browser_config: Dict[str, Any] = None,
        **kwargs
    ):
        self.agent_id = agent_id or str(uuid.uuid4())
        self.logger = AgentLogger(self.agent_id)
        self.error_handler = ErrorHandler(self.agent_id)
        
        # LLM configuration
        self.llm_provider_name = llm_provider
        self.llm_model = llm_model
        self.llm: Optional[BaseLLMProvider] = None
        
        # Browser management
        self.browser_manager = Browser<PERSON>anager(browser_config)
        self.session_manager = SessionManager()
        
        # Agent state
        self.is_initialized = False
        self.current_task = None
        self.task_history = []
        self.context = {}
        
        # Callbacks and hooks
        self.task_callbacks = {}
        self.error_callbacks = {}
        
        # Performance tracking
        self.stats = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "total_execution_time": 0.0,
            "created_at": datetime.now().isoformat()
        }
    
    async def initialize(self) -> bool:
        """Initialize the agent."""
        try:
            self.logger.info("Initializing agent")
            
            # Initialize LLM
            self.llm = create_llm(
                provider=self.llm_provider_name,
                model=self.llm_model
            )
            
            # Initialize browser
            await self.browser_manager.initialize()
            
            # Create default session
            context = self.browser_manager.context
            await self.session_manager.create_session(
                session_id="default",
                context=context,
                metadata={"agent_id": self.agent_id}
            )
            
            self.is_initialized = True
            self.logger.info("Agent initialized successfully")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agent: {e}")
            self.error_handler.handle_task_error(e, "agent_initialization")
            return False
    
    @abstractmethod
    async def execute_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Execute a task. Must be implemented by subclasses."""
        pass
    
    async def run(self, task: str, **kwargs) -> Dict[str, Any]:
        """Main entry point for running tasks."""
        if not self.is_initialized:
            await self.initialize()
        
        task_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            self.logger.log_task_start(task, task_id)
            self.current_task = {
                "id": task_id,
                "description": task,
                "started_at": start_time,
                "kwargs": kwargs
            }
            
            # Execute the task
            result = await self.execute_task(task, **kwargs)
            
            # Calculate execution time
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Update task info
            self.current_task.update({
                "completed_at": end_time,
                "execution_time": execution_time,
                "result": result
            })
            
            # Add to history
            self.task_history.append(self.current_task.copy())
            
            # Update stats
            self.stats["tasks_completed"] += 1
            self.stats["total_execution_time"] += execution_time
            
            # Log completion
            self.logger.log_task_complete(task, task_id, execution_time)
            
            # Call completion callbacks
            await self._call_task_callbacks("completed", self.current_task)
            
            return result
            
        except Exception as e:
            # Handle error
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            error_info = self.error_handler.handle_task_error(e, task)
            
            # Update task info
            self.current_task.update({
                "failed_at": end_time,
                "execution_time": execution_time,
                "error": str(e),
                "error_info": error_info
            })
            
            # Add to history
            self.task_history.append(self.current_task.copy())
            
            # Update stats
            self.stats["tasks_failed"] += 1
            self.stats["total_execution_time"] += execution_time
            
            # Take screenshot for debugging
            screenshot_data = await self._take_error_screenshot()
            if screenshot_data:
                self.logger.log_error_with_screenshot(e, screenshot_data)
            
            # Call error callbacks
            await self._call_error_callbacks(e, self.current_task)
            
            # Re-raise if not recoverable
            if not error_info.get("recoverable", False):
                raise TaskExecutionError(f"Task failed: {e}") from e
            
            return {
                "success": False,
                "error": str(e),
                "error_info": error_info,
                "task_id": task_id
            }
        
        finally:
            self.current_task = None
    
    async def _take_error_screenshot(self) -> Optional[bytes]:
        """Take a screenshot for error debugging."""
        try:
            page = await self.browser_manager.get_page()
            screenshot_path = await self.browser_manager.take_screenshot(page)
            if screenshot_path:
                with open(screenshot_path, "rb") as f:
                    return f.read()
        except:
            pass
        return None
    
    async def _call_task_callbacks(self, event: str, task_data: Dict[str, Any]):
        """Call registered task callbacks."""
        callbacks = self.task_callbacks.get(event, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task_data)
                else:
                    callback(task_data)
            except Exception as e:
                self.logger.warning(f"Task callback failed: {e}")
    
    async def _call_error_callbacks(self, error: Exception, task_data: Dict[str, Any]):
        """Call registered error callbacks."""
        callbacks = self.error_callbacks.get("error", [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(error, task_data)
                else:
                    callback(error, task_data)
            except Exception as e:
                self.logger.warning(f"Error callback failed: {e}")
    
    def register_task_callback(self, event: str, callback: Callable):
        """Register a callback for task events."""
        if event not in self.task_callbacks:
            self.task_callbacks[event] = []
        self.task_callbacks[event].append(callback)
    
    def register_error_callback(self, callback: Callable):
        """Register a callback for errors."""
        if "error" not in self.error_callbacks:
            self.error_callbacks["error"] = []
        self.error_callbacks["error"].append(callback)
    
    async def get_page_info(self) -> Dict[str, Any]:
        """Get information about the current page."""
        try:
            page = await self.browser_manager.get_page()
            
            return {
                "url": await self.browser_manager.get_current_url(page),
                "title": await self.browser_manager.get_page_title(page),
                "content_length": len(await self.browser_manager.get_page_content(page)),
                "screenshot_available": True
            }
        except Exception as e:
            self.logger.error(f"Failed to get page info: {e}")
            return {}
    
    async def save_session(self, session_name: str = "default") -> bool:
        """Save the current browser session."""
        try:
            return await self.session_manager.save_session(
                session_name,
                self.browser_manager.context
            )
        except Exception as e:
            self.logger.error(f"Failed to save session: {e}")
            return False
    
    async def load_session(self, session_name: str = "default") -> bool:
        """Load a browser session."""
        try:
            return await self.session_manager.load_session(
                session_name,
                self.browser_manager.context
            )
        except Exception as e:
            self.logger.error(f"Failed to load session: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get agent performance statistics."""
        stats = self.stats.copy()
        stats.update({
            "agent_id": self.agent_id,
            "is_initialized": self.is_initialized,
            "current_task": self.current_task is not None,
            "total_tasks": len(self.task_history),
            "success_rate": (
                self.stats["tasks_completed"] / 
                (self.stats["tasks_completed"] + self.stats["tasks_failed"])
                if (self.stats["tasks_completed"] + self.stats["tasks_failed"]) > 0
                else 0
            )
        })
        return stats
    
    def get_task_history(self, limit: int = None) -> List[Dict[str, Any]]:
        """Get task execution history."""
        history = self.task_history.copy()
        if limit:
            history = history[-limit:]
        return history
    
    async def cleanup(self):
        """Clean up agent resources."""
        try:
            self.logger.info("Cleaning up agent resources")
            
            # Save current session
            await self.save_session()
            
            # Cleanup browser
            await self.browser_manager.cleanup()
            
            # Close LLM connection
            if self.llm and hasattr(self.llm, 'close'):
                await self.llm.close()
            
            self.is_initialized = False
            self.logger.info("Agent cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
