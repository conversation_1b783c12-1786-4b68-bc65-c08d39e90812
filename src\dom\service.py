"""
Enhanced DOM service based on browser-use patterns.
Provides comprehensive DOM tree extraction, element interaction, and accessibility support.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from playwright.async_api import Page, ElementHandle, Locator
from pydantic import BaseModel, Field

from ..utils.logging_config import setup_logging
from ..utils.exceptions import DOMError, ElementNotFoundError, AccessibilityError
from ..events import default_event_bus
from ..events.browser_events import ElementFoundEvent, ElementNotFoundEvent
from ..config.service import load_config

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """DOM node types."""
    ELEMENT = "element"
    TEXT = "text"
    COMMENT = "comment"
    DOCUMENT = "document"
    DOCUMENT_TYPE = "document_type"
    DOCUMENT_FRAGMENT = "document_fragment"


@dataclass
class DOMRect:
    """DOM element bounding rectangle."""
    x: float
    y: float
    width: float
    height: float
    
    @property
    def center_x(self) -> float:
        return self.x + self.width / 2
    
    @property
    def center_y(self) -> float:
        return self.y + self.height / 2
    
    @property
    def area(self) -> float:
        return self.width * self.height
    
    def contains_point(self, x: float, y: float) -> bool:
        """Check if point is within rectangle."""
        return (self.x <= x <= self.x + self.width and 
                self.y <= y <= self.y + self.height)
    
    def intersects(self, other: 'DOMRect') -> bool:
        """Check if this rectangle intersects with another."""
        return not (self.x + self.width < other.x or 
                   other.x + other.width < self.x or
                   self.y + self.height < other.y or 
                   other.y + other.height < self.y)


@dataclass
class EnhancedDOMNode:
    """Enhanced DOM node with browser-use patterns."""
    
    # Core properties
    node_id: int
    tag_name: str
    node_type: NodeType
    text_content: Optional[str] = None
    
    # Attributes and properties
    attributes: Dict[str, str] = None
    computed_styles: Dict[str, str] = None
    
    # Positioning and visibility
    bounding_box: Optional[DOMRect] = None
    is_visible: bool = False
    is_clickable: bool = False
    is_focusable: bool = False
    
    # Hierarchy
    parent_id: Optional[int] = None
    children_ids: List[int] = None
    
    # Accessibility
    accessibility_name: Optional[str] = None
    accessibility_role: Optional[str] = None
    accessibility_description: Optional[str] = None
    
    # Interaction metadata
    interactive_index: Optional[int] = None
    selector: Optional[str] = None
    
    def __post_init__(self):
        if self.attributes is None:
            self.attributes = {}
        if self.computed_styles is None:
            self.computed_styles = {}
        if self.children_ids is None:
            self.children_ids = []


class DOMService:
    """
    Enhanced DOM service with browser-use patterns.
    
    Features:
    - Comprehensive DOM tree extraction
    - Element interaction and selection
    - Accessibility tree support
    - Performance optimizations
    - Event-driven architecture
    """
    
    def __init__(
        self,
        page: Page,
        max_depth: int = 10,
        include_invisible: bool = False,
        enable_accessibility: bool = True
    ):
        self.page = page
        self.max_depth = max_depth
        self.include_invisible = include_invisible
        self.enable_accessibility = enable_accessibility
        
        self.logger = get_logger(f"{self.__class__.__name__}")
        self.event_bus = default_event_bus
        
        # Cache for DOM state
        self._dom_cache: Dict[str, Any] = {}
        self._last_extraction_time: Optional[float] = None
        self._interactive_counter = 1
        
        # Element tracking
        self._interactive_elements: Dict[int, EnhancedDOMNode] = {}
        self._selector_map: Dict[str, int] = {}
    
    async def extract_dom_tree(self, use_cache: bool = True) -> Dict[int, EnhancedDOMNode]:
        """
        Extract the complete DOM tree with enhanced information.
        
        Args:
            use_cache: Whether to use cached DOM state if available
            
        Returns:
            Dictionary mapping node IDs to EnhancedDOMNode objects
        """
        start_time = time.time()
        
        try:
            self.logger.debug("Starting DOM tree extraction")
            
            # Check cache validity
            if use_cache and self._is_cache_valid():
                self.logger.debug("Using cached DOM tree")
                return self._dom_cache.get('nodes', {})
            
            # Extract DOM tree using Playwright
            dom_nodes = await self._extract_dom_nodes()
            
            # Enhance nodes with additional information
            enhanced_nodes = await self._enhance_nodes(dom_nodes)
            
            # Identify interactive elements
            await self._identify_interactive_elements(enhanced_nodes)
            
            # Cache the results
            self._dom_cache = {
                'nodes': enhanced_nodes,
                'extraction_time': start_time,
                'page_url': self.page.url
            }
            self._last_extraction_time = start_time
            
            extraction_time = time.time() - start_time
            self.logger.debug(
                f"DOM tree extraction completed in {extraction_time:.2f}s "
                f"({len(enhanced_nodes)} nodes, {len(self._interactive_elements)} interactive)"
            )
            
            return enhanced_nodes
            
        except Exception as e:
            self.logger.error(f"Failed to extract DOM tree: {e}")
            raise
    
    async def find_elements(
        self,
        selector: str,
        timeout: float = 5.0,
        visible_only: bool = True
    ) -> List[EnhancedDOMNode]:
        """
        Find elements matching a selector.
        
        Args:
            selector: CSS selector or XPath
            timeout: Maximum time to wait for elements
            visible_only: Only return visible elements
            
        Returns:
            List of matching EnhancedDOMNode objects
        """
        try:
            self.logger.debug(f"Finding elements with selector: {selector}")
            
            # Wait for elements to appear
            try:
                await self.page.wait_for_selector(selector, timeout=timeout * 1000)
            except Exception:
                # Element not found within timeout
                event = ElementNotFoundEvent(
                    selector=selector,
                    timeout=timeout,
                    page_url=self.page.url
                )
                await self.event_bus.emit(event)
                return []
            
            # Get all matching elements
            elements = await self.page.query_selector_all(selector)
            
            if not elements:
                event = ElementNotFoundEvent(
                    selector=selector,
                    timeout=timeout,
                    page_url=self.page.url
                )
                await self.event_bus.emit(event)
                return []
            
            # Convert to EnhancedDOMNode objects
            enhanced_elements = []
            for i, element in enumerate(elements):
                try:
                    enhanced_node = await self._element_to_enhanced_node(element, i)
                    
                    # Filter by visibility if requested
                    if visible_only and not enhanced_node.is_visible:
                        continue
                    
                    enhanced_elements.append(enhanced_node)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to enhance element {i}: {e}")
                    continue
            
            # Emit found event
            event = ElementFoundEvent(
                selector=selector,
                element_count=len(enhanced_elements),
                page_url=self.page.url
            )
            await self.event_bus.emit(event)
            
            self.logger.debug(f"Found {len(enhanced_elements)} elements matching {selector}")
            return enhanced_elements
            
        except Exception as e:
            self.logger.error(f"Failed to find elements with selector {selector}: {e}")
            raise
    
    async def get_interactive_elements(self) -> Dict[int, EnhancedDOMNode]:
        """Get all interactive elements with their indices."""
        if not self._interactive_elements:
            await self.extract_dom_tree()
        
        return self._interactive_elements.copy()
    
    async def get_element_by_index(self, index: int) -> Optional[EnhancedDOMNode]:
        """Get an interactive element by its index."""
        interactive_elements = await self.get_interactive_elements()
        
        for node in interactive_elements.values():
            if node.interactive_index == index:
                return node
        
        return None
    
    async def click_element(self, element: Union[EnhancedDOMNode, int, str]) -> bool:
        """
        Click an element by node, index, or selector.
        
        Args:
            element: EnhancedDOMNode, interactive index, or CSS selector
            
        Returns:
            True if click was successful
        """
        try:
            target_element = await self._resolve_element(element)
            if not target_element:
                return False
            
            # Use selector if available, otherwise use bounding box
            if target_element.selector:
                await self.page.click(target_element.selector)
            elif target_element.bounding_box:
                await self.page.click(
                    target_element.bounding_box.center_x,
                    target_element.bounding_box.center_y
                )
            else:
                self.logger.error("Cannot click element: no selector or bounding box")
                return False
            
            self.logger.debug(f"Successfully clicked element: {target_element.tag_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to click element: {e}")
            return False
    
    def _is_cache_valid(self) -> bool:
        """Check if the DOM cache is still valid."""
        if not self._dom_cache or not self._last_extraction_time:
            return False
        
        # Cache is valid for 30 seconds
        cache_age = time.time() - self._last_extraction_time
        if cache_age > 30:
            return False
        
        # Check if page URL has changed
        cached_url = self._dom_cache.get('page_url')
        if cached_url != self.page.url:
            return False
        
        return True
    
    async def _resolve_element(self, element: Union[EnhancedDOMNode, int, str]) -> Optional[EnhancedDOMNode]:
        """Resolve different element representations to EnhancedDOMNode."""
        if isinstance(element, EnhancedDOMNode):
            return element
        elif isinstance(element, int):
            return await self.get_element_by_index(element)
        elif isinstance(element, str):
            elements = await self.find_elements(element)
            return elements[0] if elements else None
        else:
            return None

    async def _extract_dom_nodes(self) -> Dict[int, Dict[str, Any]]:
        """Extract raw DOM nodes using Playwright."""
        # Use Playwright's evaluate to get DOM tree
        dom_data = await self.page.evaluate("""
            () => {
                const nodes = {};
                let nodeId = 1;

                function extractNode(element, depth = 0) {
                    if (depth > 10) return null; // Prevent infinite recursion

                    const id = nodeId++;
                    const rect = element.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(element);

                    const node = {
                        id: id,
                        tagName: element.tagName?.toLowerCase() || '',
                        nodeType: element.nodeType,
                        textContent: element.textContent?.trim() || '',
                        attributes: {},
                        boundingBox: {
                            x: rect.x,
                            y: rect.y,
                            width: rect.width,
                            height: rect.height
                        },
                        isVisible: rect.width > 0 && rect.height > 0 &&
                                  computedStyle.visibility !== 'hidden' &&
                                  computedStyle.display !== 'none',
                        computedStyles: {
                            display: computedStyle.display,
                            visibility: computedStyle.visibility,
                            position: computedStyle.position,
                            zIndex: computedStyle.zIndex
                        },
                        children: []
                    };

                    // Extract attributes
                    if (element.attributes) {
                        for (let attr of element.attributes) {
                            node.attributes[attr.name] = attr.value;
                        }
                    }

                    // Extract children
                    for (let child of element.children) {
                        const childNode = extractNode(child, depth + 1);
                        if (childNode) {
                            node.children.push(childNode.id);
                            nodes[childNode.id] = childNode;
                        }
                    }

                    return node;
                }

                const rootNode = extractNode(document.documentElement);
                if (rootNode) {
                    nodes[rootNode.id] = rootNode;
                }

                return nodes;
            }
        """)

        return dom_data

    async def _enhance_nodes(self, raw_nodes: Dict[int, Dict[str, Any]]) -> Dict[int, EnhancedDOMNode]:
        """Convert raw DOM data to EnhancedDOMNode objects."""
        enhanced_nodes = {}

        for node_id, raw_node in raw_nodes.items():
            try:
                # Create bounding box
                bbox_data = raw_node.get('boundingBox', {})
                bounding_box = DOMRect(
                    x=bbox_data.get('x', 0),
                    y=bbox_data.get('y', 0),
                    width=bbox_data.get('width', 0),
                    height=bbox_data.get('height', 0)
                ) if bbox_data else None

                # Determine node type
                node_type_map = {
                    1: NodeType.ELEMENT,
                    3: NodeType.TEXT,
                    8: NodeType.COMMENT,
                    9: NodeType.DOCUMENT,
                    10: NodeType.DOCUMENT_TYPE,
                    11: NodeType.DOCUMENT_FRAGMENT
                }
                node_type = node_type_map.get(raw_node.get('nodeType', 1), NodeType.ELEMENT)

                # Create enhanced node
                enhanced_node = EnhancedDOMNode(
                    node_id=node_id,
                    tag_name=raw_node.get('tagName', ''),
                    node_type=node_type,
                    text_content=raw_node.get('textContent'),
                    attributes=raw_node.get('attributes', {}),
                    computed_styles=raw_node.get('computedStyles', {}),
                    bounding_box=bounding_box,
                    is_visible=raw_node.get('isVisible', False),
                    children_ids=raw_node.get('children', [])
                )

                # Determine if element is clickable or focusable
                enhanced_node.is_clickable = self._is_clickable_element(enhanced_node)
                enhanced_node.is_focusable = self._is_focusable_element(enhanced_node)

                # Extract accessibility information
                if self.enable_accessibility:
                    await self._extract_accessibility_info(enhanced_node)

                enhanced_nodes[node_id] = enhanced_node

            except Exception as e:
                self.logger.warning(f"Failed to enhance node {node_id}: {e}")
                continue

        return enhanced_nodes

    async def _identify_interactive_elements(self, nodes: Dict[int, EnhancedDOMNode]):
        """Identify and index interactive elements."""
        self._interactive_elements.clear()
        self._selector_map.clear()
        self._interactive_counter = 1

        for node in nodes.values():
            if self._is_interactive_element(node):
                node.interactive_index = self._interactive_counter
                self._interactive_elements[self._interactive_counter] = node

                # Generate selector
                selector = self._generate_selector(node)
                if selector:
                    node.selector = selector
                    self._selector_map[selector] = node.node_id

                self._interactive_counter += 1

    def _is_clickable_element(self, node: EnhancedDOMNode) -> bool:
        """Determine if an element is clickable."""
        clickable_tags = {'a', 'button', 'input', 'select', 'textarea', 'option'}
        clickable_roles = {'button', 'link', 'menuitem', 'tab', 'checkbox', 'radio'}

        if node.tag_name in clickable_tags:
            return True

        role = node.attributes.get('role', '').lower()
        if role in clickable_roles:
            return True

        # Check for click handlers (simplified)
        if any(attr.startswith('on') for attr in node.attributes.keys()):
            return True

        return False

    def _is_focusable_element(self, node: EnhancedDOMNode) -> bool:
        """Determine if an element is focusable."""
        focusable_tags = {'input', 'select', 'textarea', 'button', 'a'}

        if node.tag_name in focusable_tags:
            return True

        # Check tabindex
        tabindex = node.attributes.get('tabindex')
        if tabindex is not None:
            try:
                return int(tabindex) >= 0
            except ValueError:
                pass

        return False

    def _is_interactive_element(self, node: EnhancedDOMNode) -> bool:
        """Determine if an element should be included in interactive elements."""
        if not node.is_visible and not self.include_invisible:
            return False

        return node.is_clickable or node.is_focusable

    def _generate_selector(self, node: EnhancedDOMNode) -> Optional[str]:
        """Generate a CSS selector for the element."""
        # Simple selector generation - in production, this would be more sophisticated
        if node.attributes.get('id'):
            return f"#{node.attributes['id']}"

        if node.attributes.get('class'):
            classes = node.attributes['class'].split()
            if classes:
                return f"{node.tag_name}.{'.'.join(classes)}"

        return node.tag_name if node.tag_name else None

    async def _extract_accessibility_info(self, node: EnhancedDOMNode):
        """Extract accessibility information for the node."""
        # Extract aria-label, aria-labelledby, etc.
        node.accessibility_name = (
            node.attributes.get('aria-label') or
            node.attributes.get('title') or
            node.text_content
        )

        node.accessibility_role = (
            node.attributes.get('role') or
            self._get_implicit_role(node.tag_name)
        )

        node.accessibility_description = node.attributes.get('aria-describedby')

    def _get_implicit_role(self, tag_name: str) -> Optional[str]:
        """Get the implicit ARIA role for a tag."""
        role_map = {
            'button': 'button',
            'a': 'link',
            'input': 'textbox',  # Simplified
            'select': 'combobox',
            'textarea': 'textbox',
            'img': 'img',
            'h1': 'heading',
            'h2': 'heading',
            'h3': 'heading',
            'h4': 'heading',
            'h5': 'heading',
            'h6': 'heading'
        }

        return role_map.get(tag_name)

    async def _element_to_enhanced_node(self, element: ElementHandle, index: int) -> EnhancedDOMNode:
        """Convert a Playwright ElementHandle to EnhancedDOMNode."""
        # Get element properties
        tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
        text_content = await element.text_content()

        # Get bounding box
        bbox = await element.bounding_box()
        bounding_box = DOMRect(
            x=bbox['x'],
            y=bbox['y'],
            width=bbox['width'],
            height=bbox['height']
        ) if bbox else None

        # Get attributes
        attributes = await element.evaluate("""
            el => {
                const attrs = {};
                for (let attr of el.attributes) {
                    attrs[attr.name] = attr.value;
                }
                return attrs;
            }
        """)

        # Check visibility
        is_visible = await element.is_visible()

        # Create enhanced node
        node = EnhancedDOMNode(
            node_id=index,
            tag_name=tag_name,
            node_type=NodeType.ELEMENT,
            text_content=text_content,
            attributes=attributes,
            bounding_box=bounding_box,
            is_visible=is_visible
        )

        # Set interaction properties
        node.is_clickable = self._is_clickable_element(node)
        node.is_focusable = self._is_focusable_element(node)

        return node

    async def get_enhanced_dom_tree(
        self,
        include_accessibility: bool = True,
        include_iframes: bool = True,
        max_iframe_depth: int = 5
    ) -> Dict[str, Any]:
        """Get enhanced DOM tree with accessibility and iframe support based on browser-use patterns."""
        start_time = time.time()

        try:
            # Get basic DOM tree
            dom_nodes = await self.get_dom_tree()

            # Enhanced processing
            enhanced_tree = {
                'nodes': dom_nodes,
                'metadata': {
                    'extraction_time': time.time() - start_time,
                    'page_url': self.page.url,
                    'page_title': await self.page.title(),
                    'viewport': await self.page.viewport_size(),
                    'device_pixel_ratio': await self.page.evaluate('window.devicePixelRatio'),
                    'timestamp': time.time()
                },
                'interactive_elements': self._interactive_elements,
                'accessibility_tree': None,
                'iframe_trees': []
            }

            # Add accessibility information if requested
            if include_accessibility:
                try:
                    enhanced_tree['accessibility_tree'] = await self._extract_accessibility_tree()
                except Exception as e:
                    self.logger.warning(f"Failed to extract accessibility tree: {e}")

            # Process iframes if requested
            if include_iframes:
                try:
                    enhanced_tree['iframe_trees'] = await self._extract_iframe_trees(max_iframe_depth)
                except Exception as e:
                    self.logger.warning(f"Failed to extract iframe trees: {e}")

            # Add performance metrics
            enhanced_tree['performance'] = {
                'total_nodes': len(dom_nodes),
                'interactive_nodes': len(self._interactive_elements),
                'extraction_time': time.time() - start_time,
                'memory_usage': await self._get_memory_usage()
            }

            return enhanced_tree

        except Exception as e:
            self.logger.error(f"Failed to get enhanced DOM tree: {e}")
            raise DOMError(f"Enhanced DOM tree extraction failed: {e}")

    async def _extract_accessibility_tree(self) -> Dict[str, Any]:
        """Extract accessibility tree information."""
        try:
            # Get accessibility tree using CDP if available
            ax_tree = await self.page.evaluate("""
                () => {
                    const getAccessibilityInfo = (element) => {
                        if (!element) return null;

                        return {
                            role: element.getAttribute('role') || element.tagName.toLowerCase(),
                            name: element.getAttribute('aria-label') ||
                                  element.getAttribute('alt') ||
                                  element.textContent?.trim() || '',
                            description: element.getAttribute('aria-describedby') || '',
                            value: element.value || element.getAttribute('aria-valuenow') || '',
                            properties: {
                                checked: element.checked || element.getAttribute('aria-checked') === 'true',
                                selected: element.selected || element.getAttribute('aria-selected') === 'true',
                                expanded: element.getAttribute('aria-expanded') === 'true',
                                disabled: element.disabled || element.getAttribute('aria-disabled') === 'true',
                                required: element.required || element.getAttribute('aria-required') === 'true',
                                readonly: element.readOnly || element.getAttribute('aria-readonly') === 'true'
                            }
                        };
                    };

                    const elements = document.querySelectorAll('*');
                    const axTree = {};

                    elements.forEach((el, index) => {
                        const info = getAccessibilityInfo(el);
                        if (info && (info.name || info.role !== el.tagName.toLowerCase())) {
                            axTree[index] = info;
                        }
                    });

                    return axTree;
                }
            """)

            return ax_tree

        except Exception as e:
            self.logger.warning(f"Failed to extract accessibility tree: {e}")
            return {}

    async def _extract_iframe_trees(self, max_depth: int) -> List[Dict[str, Any]]:
        """Extract DOM trees from iframes."""
        iframe_trees = []

        try:
            # Find all iframes
            iframes = await self.page.query_selector_all('iframe')

            for i, iframe in enumerate(iframes):
                if i >= 100:  # Limit number of iframes processed
                    break

                try:
                    # Get iframe source
                    src = await iframe.get_attribute('src')
                    if not src:
                        continue

                    # Create iframe tree info
                    iframe_info = {
                        'index': i,
                        'src': src,
                        'bounding_box': await self._get_element_bounding_box(iframe),
                        'is_cross_origin': await self._is_cross_origin_iframe(iframe),
                        'dom_tree': None
                    }

                    # Try to extract iframe DOM if same-origin
                    if not iframe_info['is_cross_origin'] and max_depth > 0:
                        try:
                            iframe_content = await iframe.content_frame()
                            if iframe_content:
                                # Create DOM service for iframe
                                iframe_dom_service = EnhancedDOMService(iframe_content)
                                iframe_info['dom_tree'] = await iframe_dom_service.get_dom_tree()
                        except Exception as e:
                            self.logger.debug(f"Failed to extract iframe {i} DOM: {e}")

                    iframe_trees.append(iframe_info)

                except Exception as e:
                    self.logger.debug(f"Failed to process iframe {i}: {e}")
                    continue

            return iframe_trees

        except Exception as e:
            self.logger.warning(f"Failed to extract iframe trees: {e}")
            return []

    async def _is_cross_origin_iframe(self, iframe: ElementHandle) -> bool:
        """Check if iframe is cross-origin."""
        try:
            # Try to access iframe content
            frame = await iframe.content_frame()
            if frame:
                await frame.evaluate('window.location.href')
                return False
        except Exception:
            return True
        return True

    async def _get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage information."""
        try:
            memory_info = await self.page.evaluate("""
                () => {
                    if (performance.memory) {
                        return {
                            used: performance.memory.usedJSHeapSize,
                            total: performance.memory.totalJSHeapSize,
                            limit: performance.memory.jsHeapSizeLimit
                        };
                    }
                    return null;
                }
            """)
            return memory_info or {}
        except Exception:
            return {}

    def serialize_dom_tree(self, dom_tree: Dict[str, Any], include_invisible: bool = False) -> str:
        """Serialize DOM tree for LLM consumption based on browser-use patterns."""
        try:
            serialized_elements = []

            for node_id, node in dom_tree.get('nodes', {}).items():
                if not include_invisible and not node.is_visible:
                    continue

                # Create element representation
                element_info = {
                    'index': node_id,
                    'tag': node.tag_name,
                    'text': node.text_content[:100] if node.text_content else '',
                    'attributes': {k: v for k, v in node.attributes.items()
                                 if k in ['id', 'class', 'type', 'name', 'value', 'placeholder', 'title', 'role', 'aria-label']},
                    'interactive': node.is_clickable or node.is_focusable,
                    'visible': node.is_visible
                }

                # Add bounding box if available
                if node.bounding_box:
                    element_info['bounds'] = {
                        'x': round(node.bounding_box.x),
                        'y': round(node.bounding_box.y),
                        'width': round(node.bounding_box.width),
                        'height': round(node.bounding_box.height)
                    }

                serialized_elements.append(element_info)

            # Sort by visibility and interactivity
            serialized_elements.sort(key=lambda x: (not x['interactive'], not x['visible'], x.get('bounds', {}).get('y', 0)))

            return str(serialized_elements)

        except Exception as e:
            self.logger.error(f"Failed to serialize DOM tree: {e}")
            return "[]"
