"""
Custom browser automation tools for specialized tasks.
"""

import asyncio
import json
import re
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from pathlib import Path

from ..utils import get_logger, safe_execute_async


class CustomTools:
    """Custom tools for specialized browser automation tasks."""
    
    def __init__(self, browser_manager, core_tools, logger=None):
        self.browser_manager = browser_manager
        self.core_tools = core_tools
        self.logger = logger or get_logger(__name__)
    
    async def smart_form_filler(self, form_data: Dict[str, Any], form_selector: str = "form") -> Dict[str, Any]:
        """Intelligently fill out forms by detecting field types."""
        try:
            page = await self.browser_manager.get_page()
            
            # Find the form
            form_element = await page.query_selector(form_selector)
            if not form_element:
                return {"success": False, "error": "Form not found"}
            
            # Get all form fields
            fields = await form_element.query_selector_all("input, select, textarea")
            
            filled_fields = []
            for field in fields:
                try:
                    # Get field attributes
                    field_type = await field.get_attribute("type") or "text"
                    field_name = await field.get_attribute("name") or ""
                    field_id = await field.get_attribute("id") or ""
                    field_placeholder = await field.get_attribute("placeholder") or ""
                    
                    # Determine what data to fill based on field characteristics
                    field_key = self._match_field_to_data(
                        field_name, field_id, field_placeholder, field_type, form_data
                    )
                    
                    if field_key and field_key in form_data:
                        value = form_data[field_key]
                        
                        if field_type == "select":
                            await field.select_option(value=str(value))
                        elif field_type in ["checkbox", "radio"]:
                            if value:
                                await field.check()
                        else:
                            await field.fill(str(value))
                        
                        filled_fields.append({
                            "field": field_name or field_id,
                            "type": field_type,
                            "value": value
                        })
                
                except Exception as e:
                    self.logger.warning(f"Failed to fill field: {e}")
            
            return {
                "success": True,
                "filled_fields": filled_fields,
                "total_fields": len(filled_fields)
            }
            
        except Exception as e:
            self.logger.error(f"Smart form filling failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _match_field_to_data(self, name: str, field_id: str, placeholder: str, field_type: str, data: Dict[str, Any]) -> Optional[str]:
        """Match form field to data key based on field characteristics."""
        # Combine all field identifiers
        field_text = f"{name} {field_id} {placeholder}".lower()
        
        # Common field mappings
        mappings = {
            "email": ["email", "e-mail", "mail"],
            "password": ["password", "pass", "pwd"],
            "first_name": ["first", "fname", "firstname", "given"],
            "last_name": ["last", "lname", "lastname", "surname", "family"],
            "name": ["name", "fullname", "full_name"],
            "phone": ["phone", "tel", "telephone", "mobile"],
            "address": ["address", "street", "addr"],
            "city": ["city", "town"],
            "state": ["state", "province", "region"],
            "zip": ["zip", "postal", "postcode"],
            "country": ["country", "nation"],
            "company": ["company", "organization", "org"],
            "title": ["title", "position", "job"],
            "website": ["website", "url", "site"],
            "message": ["message", "comment", "note", "description"]
        }
        
        # Check for exact matches first
        for data_key in data.keys():
            if data_key.lower() in field_text:
                return data_key
        
        # Check for pattern matches
        for data_key, patterns in mappings.items():
            if data_key in data:
                for pattern in patterns:
                    if pattern in field_text:
                        return data_key
        
        return None
    
    async def table_extractor(self, table_selector: str = "table") -> List[Dict[str, Any]]:
        """Extract data from HTML tables."""
        try:
            page = await self.browser_manager.get_page()
            
            # Find all tables matching the selector
            tables = await page.query_selector_all(table_selector)
            
            extracted_data = []
            
            for table_index, table in enumerate(tables):
                # Get headers
                header_rows = await table.query_selector_all("thead tr, tr:first-child")
                headers = []
                
                if header_rows:
                    header_cells = await header_rows[0].query_selector_all("th, td")
                    for cell in header_cells:
                        header_text = await cell.text_content()
                        headers.append(header_text.strip())
                
                # Get data rows
                data_rows = await table.query_selector_all("tbody tr, tr:not(:first-child)")
                
                table_data = []
                for row in data_rows:
                    cells = await row.query_selector_all("td, th")
                    row_data = {}
                    
                    for cell_index, cell in enumerate(cells):
                        cell_text = await cell.text_content()
                        header = headers[cell_index] if cell_index < len(headers) else f"Column_{cell_index + 1}"
                        row_data[header] = cell_text.strip()
                    
                    if row_data:  # Only add non-empty rows
                        table_data.append(row_data)
                
                extracted_data.append({
                    "table_index": table_index,
                    "headers": headers,
                    "data": table_data,
                    "row_count": len(table_data)
                })
            
            self.logger.debug(f"Extracted data from {len(extracted_data)} tables")
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"Table extraction failed: {e}")
            return []
    
    async def infinite_scroll_handler(self, max_scrolls: int = 10, scroll_pause: float = 2.0) -> Dict[str, Any]:
        """Handle infinite scroll pages to load more content."""
        try:
            page = await self.browser_manager.get_page()
            
            initial_height = await page.evaluate("document.body.scrollHeight")
            scroll_count = 0
            
            while scroll_count < max_scrolls:
                # Scroll to bottom
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                
                # Wait for new content to load
                await asyncio.sleep(scroll_pause)
                
                # Check if new content was loaded
                new_height = await page.evaluate("document.body.scrollHeight")
                
                if new_height == initial_height:
                    # No new content loaded, break
                    break
                
                initial_height = new_height
                scroll_count += 1
                
                self.logger.debug(f"Infinite scroll: {scroll_count}/{max_scrolls}")
            
            return {
                "success": True,
                "scrolls_performed": scroll_count,
                "final_height": initial_height
            }
            
        except Exception as e:
            self.logger.error(f"Infinite scroll handling failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def wait_for_ajax(self, timeout: int = 30000) -> bool:
        """Wait for AJAX requests to complete."""
        try:
            page = await self.browser_manager.get_page()
            
            # Wait for jQuery AJAX if present
            await page.wait_for_function(
                "() => typeof jQuery === 'undefined' || jQuery.active === 0",
                timeout=timeout
            )
            
            # Wait for fetch requests to complete
            await page.wait_for_function(
                "() => window.fetch === undefined || window.fetchCount === 0",
                timeout=timeout
            )
            
            self.logger.debug("AJAX requests completed")
            return True
            
        except Exception as e:
            self.logger.warning(f"AJAX wait timeout: {e}")
            return False
    
    async def cookie_manager(self, action: str, cookies: List[Dict] = None, domain: str = None) -> Dict[str, Any]:
        """Manage browser cookies."""
        try:
            context = self.browser_manager.context
            
            if action == "get":
                all_cookies = await context.cookies()
                if domain:
                    filtered_cookies = [c for c in all_cookies if domain in c.get("domain", "")]
                    return {"success": True, "cookies": filtered_cookies}
                return {"success": True, "cookies": all_cookies}
            
            elif action == "set" and cookies:
                await context.add_cookies(cookies)
                return {"success": True, "message": f"Added {len(cookies)} cookies"}
            
            elif action == "clear":
                await context.clear_cookies()
                return {"success": True, "message": "All cookies cleared"}
            
            else:
                return {"success": False, "error": "Invalid action or missing cookies"}
                
        except Exception as e:
            self.logger.error(f"Cookie management failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def element_highlighter(self, selector: str, duration: float = 2.0) -> bool:
        """Highlight an element for debugging purposes."""
        try:
            page = await self.browser_manager.get_page()
            
            # Add highlight styles
            await page.add_style_tag(content="""
                .browser-automation-highlight {
                    outline: 3px solid red !important;
                    outline-offset: 2px !important;
                    background-color: rgba(255, 255, 0, 0.3) !important;
                }
            """)
            
            # Apply highlight to element
            await page.evaluate(f"""
                const element = document.querySelector('{selector}');
                if (element) {{
                    element.classList.add('browser-automation-highlight');
                    setTimeout(() => {{
                        element.classList.remove('browser-automation-highlight');
                    }}, {duration * 1000});
                }}
            """)
            
            self.logger.debug(f"Highlighted element: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"Element highlighting failed: {e}")
            return False
    
    async def performance_monitor(self) -> Dict[str, Any]:
        """Monitor page performance metrics."""
        try:
            page = await self.browser_manager.get_page()
            
            # Get performance metrics
            metrics = await page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const paint = performance.getEntriesByType('paint');
                    
                    return {
                        loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
                        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
                        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                        resourceCount: performance.getEntriesByType('resource').length
                    };
                }
            """)
            
            return {
                "success": True,
                "metrics": metrics,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Performance monitoring failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def accessibility_checker(self) -> Dict[str, Any]:
        """Check basic accessibility issues on the page."""
        try:
            page = await self.browser_manager.get_page()
            
            # Check for common accessibility issues
            issues = await page.evaluate("""
                () => {
                    const issues = [];
                    
                    // Check for images without alt text
                    const images = document.querySelectorAll('img:not([alt])');
                    if (images.length > 0) {
                        issues.push({
                            type: 'missing_alt_text',
                            count: images.length,
                            message: 'Images without alt text found'
                        });
                    }
                    
                    // Check for form inputs without labels
                    const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
                    const unlabeledInputs = Array.from(inputs).filter(input => {
                        const id = input.id;
                        return !id || !document.querySelector(`label[for="${id}"]`);
                    });
                    
                    if (unlabeledInputs.length > 0) {
                        issues.push({
                            type: 'unlabeled_inputs',
                            count: unlabeledInputs.length,
                            message: 'Form inputs without proper labels found'
                        });
                    }
                    
                    // Check for low contrast (basic check)
                    const elements = document.querySelectorAll('*');
                    let lowContrastCount = 0;
                    
                    // This is a simplified check - in practice, you'd use a proper contrast ratio calculator
                    Array.from(elements).slice(0, 100).forEach(el => {
                        const style = window.getComputedStyle(el);
                        const color = style.color;
                        const backgroundColor = style.backgroundColor;
                        
                        if (color === 'rgb(128, 128, 128)' && backgroundColor === 'rgb(255, 255, 255)') {
                            lowContrastCount++;
                        }
                    });
                    
                    if (lowContrastCount > 0) {
                        issues.push({
                            type: 'potential_low_contrast',
                            count: lowContrastCount,
                            message: 'Elements with potentially low contrast found'
                        });
                    }
                    
                    return issues;
                }
            """)
            
            return {
                "success": True,
                "issues": issues,
                "total_issues": len(issues),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Accessibility check failed: {e}")
            return {"success": False, "error": str(e)}
