"""
Google Gemini LLM provider implementation.
"""

import json
import asyncio
import time
from typing import Dict, Any, Optional, List
import httpx

from ..llm_factory import BaseLLMProvider
from ..base import BaseChatModel, RateLimitError, AuthenticationError, ModelNotFoundError
from ..views import ChatInvokeCompletion, ChatInvokeUsage, ModelCapabilities, ProviderConfig
from ..messages import BaseMessage
from ...utils import get_logger, LLMError, retry_on_exception, NetworkError

logger = get_logger(__name__)


class GeminiProvider(BaseLLMProvider):
    """Google Gemini LLM provider."""
    
    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp", **kwargs):
        # Create ProviderConfig for browser-use compatibility
        config = ProviderConfig(
            provider_name="gemini",
            model_name=model,
            api_key=api_key,
            **{k: v for k, v in kwargs.items() if k in ProviderConfig.__fields__}
        )
        super().__init__(config)

        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.client = httpx.AsyncClient(timeout=60.0)
        self.model = model
        self._verified_api_keys = False
        
        # Default generation config
        self.generation_config = {
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 0.8),
            "top_k": kwargs.get("top_k", 40),
            "max_output_tokens": kwargs.get("max_output_tokens", 8192),
        }

    @property
    def provider(self) -> str:
        """Get the provider name."""
        return "gemini"

    @property
    def name(self) -> str:
        """Get the model name."""
        return self.model

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "gemini"

    async def initialize(self) -> bool:
        """Initialize the provider."""
        if self._initialized:
            return True

        try:
            # Validate API key
            if await self.validate_api_key():
                self._initialized = True
                self._verified_api_keys = True
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to initialize Gemini provider: {e}")
            return False

    async def get_capabilities(self) -> ModelCapabilities:
        """Get model capabilities."""
        if self._capabilities:
            return self._capabilities

        # Define capabilities for Gemini models
        self._capabilities = ModelCapabilities(
            supports_vision=True,
            supports_function_calling=True,
            supports_structured_output=True,
            supports_streaming=True,
            supports_caching=False,  # Gemini doesn't support prompt caching yet
            supports_thinking=False,
            max_tokens=8192,
            max_context_length=1000000,  # Gemini 2.0 has 1M context
            max_output_tokens=8192,
            input_cost_per_1k=0.00015,  # Approximate costs
            output_cost_per_1k=0.0006
        )

        return self._capabilities

    def validate_configuration(self) -> bool:
        """Validate Gemini configuration."""
        if not self.api_key:
            raise LLMError("Gemini API key is required")
        
        if not self.model:
            raise LLMError("Gemini model name is required")
        
        return True
    
    @retry_on_exception((NetworkError, httpx.RequestError))
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Generate a response using Gemini."""
        try:
            # Prepare the request
            url = f"{self.base_url}/models/{self.model}:generateContent"
            
            # Merge generation config with kwargs
            generation_config = {**self.generation_config, **kwargs}
            
            payload = {
                "contents": [
                    {
                        "parts": [
                            {"text": prompt}
                        ]
                    }
                ],
                "generationConfig": generation_config
            }
            
            headers = {
                "Content-Type": "application/json",
            }
            
            params = {
                "key": self.api_key
            }
            
            self.logger.debug(
                "Sending request to Gemini",
                model=self.model,
                prompt_length=len(prompt)
            )
            
            response = await self.client.post(
                url,
                json=payload,
                headers=headers,
                params=params
            )
            
            if response.status_code != 200:
                error_msg = f"Gemini API error: {response.status_code} - {response.text}"
                self.logger.error(error_msg)
                raise NetworkError(error_msg)
            
            result = response.json()
            
            # Extract the generated text
            if "candidates" in result and len(result["candidates"]) > 0:
                candidate = result["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "text" in parts[0]:
                        generated_text = parts[0]["text"]
                        
                        self.logger.debug(
                            "Received response from Gemini",
                            response_length=len(generated_text)
                        )
                        
                        return generated_text
            
            # Handle safety filters or other issues
            if "candidates" in result and len(result["candidates"]) > 0:
                candidate = result["candidates"][0]
                if "finishReason" in candidate:
                    finish_reason = candidate["finishReason"]
                    if finish_reason == "SAFETY":
                        raise LLMError("Response blocked by Gemini safety filters")
                    elif finish_reason == "RECITATION":
                        raise LLMError("Response blocked due to recitation concerns")
            
            raise LLMError(f"Unexpected response format from Gemini: {result}")
            
        except httpx.RequestError as e:
            self.logger.error(f"Network error calling Gemini: {e}")
            raise NetworkError(f"Network error calling Gemini: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON response from Gemini: {e}")
            raise LLMError(f"Invalid JSON response from Gemini: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error calling Gemini: {e}")
            raise LLMError(f"Unexpected error calling Gemini: {e}")
    
    async def generate_structured_response(self, prompt: str, schema: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Generate a structured response using Gemini with JSON schema."""
        try:
            # Add JSON schema instruction to prompt
            schema_prompt = f"""
{prompt}

Please respond with a valid JSON object that matches this schema:
{json.dumps(schema, indent=2)}

Respond only with the JSON object, no additional text or formatting.
"""
            
            response_text = await self.generate_response(schema_prompt, **kwargs)
            
            # Try to parse as JSON
            try:
                # Clean up the response (remove markdown formatting if present)
                cleaned_response = response_text.strip()
                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith("```"):
                    cleaned_response = cleaned_response[:-3]
                cleaned_response = cleaned_response.strip()
                
                result = json.loads(cleaned_response)
                
                self.logger.debug(
                    "Successfully parsed structured response",
                    schema_keys=list(schema.keys()) if isinstance(schema, dict) else None
                )
                
                return result
                
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse JSON response: {e}")
                self.logger.debug(f"Raw response: {response_text}")
                raise LLMError(f"Failed to parse structured response as JSON: {e}")
                
        except Exception as e:
            self.logger.error(f"Error generating structured response: {e}")
            raise
    
    async def generate_with_tools(self, prompt: str, tools: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """Generate response with function calling capabilities."""
        try:
            url = f"{self.base_url}/models/{self.model}:generateContent"
            
            generation_config = {**self.generation_config, **kwargs}
            
            payload = {
                "contents": [
                    {
                        "parts": [
                            {"text": prompt}
                        ]
                    }
                ],
                "tools": [
                    {
                        "function_declarations": tools
                    }
                ],
                "generationConfig": generation_config
            }
            
            headers = {
                "Content-Type": "application/json",
            }
            
            params = {
                "key": self.api_key
            }
            
            response = await self.client.post(
                url,
                json=payload,
                headers=headers,
                params=params
            )
            
            if response.status_code != 200:
                error_msg = f"Gemini API error: {response.status_code} - {response.text}"
                raise NetworkError(error_msg)
            
            result = response.json()
            
            self.logger.debug(
                "Received tool response from Gemini",
                has_candidates=bool(result.get("candidates"))
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating response with tools: {e}")
            raise
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            asyncio.create_task(self.close())
        except:
            pass

    async def ainvoke(self, messages: List[BaseMessage], output_format=None) -> ChatInvokeCompletion:
        """
        Browser-use style ainvoke method for compatibility.

        Args:
            messages: List of BaseMessage objects
            output_format: Optional structured output format

        Returns:
            ChatInvokeCompletion with response and usage information
        """
        try:
            start_time = time.time()

            # Convert messages to prompt
            prompt = self._messages_to_prompt(messages)

            if output_format:
                # Generate structured response
                if hasattr(output_format, 'model_json_schema'):
                    schema = output_format.model_json_schema()
                else:
                    schema = output_format

                result = await self.generate_structured_response(prompt, schema)
                content = result if isinstance(result, str) else json.dumps(result)
            else:
                # Generate regular response
                content = await self.generate_response(prompt)

            # Calculate response time
            response_time = time.time() - start_time

            # Create usage information (simplified - real implementation would track actual tokens)
            usage = ChatInvokeUsage(
                prompt_tokens=len(prompt.split()) * 2,  # Rough estimate
                completion_tokens=len(content.split()) * 2,  # Rough estimate
                total_tokens=len(prompt.split()) * 2 + len(content.split()) * 2,
                model_name=self.model,
                provider="gemini"
            )

            # Calculate cost
            capabilities = await self.get_capabilities()
            if capabilities.input_cost_per_1k and capabilities.output_cost_per_1k:
                usage.calculate_total_cost(
                    capabilities.input_cost_per_1k,
                    capabilities.output_cost_per_1k
                )

            return ChatInvokeCompletion(
                completion=content,
                usage=usage,
                model_name=self.model,
                provider="gemini",
                response_time=response_time
            )

        except Exception as e:
            self.logger.error(f"ainvoke failed: {e}")
            raise

    def _messages_to_prompt(self, messages: List[BaseMessage]) -> str:
        """Convert BaseMessage list to a single prompt string."""
        prompt_parts = []

        for message in messages:
            if message.role == "system":
                prompt_parts.append(f"System: {message.content}")
            elif message.role == "user":
                prompt_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                prompt_parts.append(f"Assistant: {message.content}")

        return "\n\n".join(prompt_parts)
